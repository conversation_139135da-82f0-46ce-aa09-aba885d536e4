# 🎨 AI Fun Lab - Design Guidelines

## 目标用户
为 **6-11岁小学生** 设计安全、有趣、易懂的AI创作平台

## 核心设计原则

### 1. 现实隐喻 & 可见性
- **图标 + 文字标签**: 所有功能都有清晰的图标和文字说明
- **熟悉元素**: 使用儿童熟悉的隐喻（画笔、魔法棒、相机等）
- **避免隐藏**: 重要功能始终可见，不使用汉堡菜单或下拉菜单
- **面包屑导航**: 让孩子知道自己在哪里

### 2. 色彩系统
**主色板** - 温暖友好，高对比度
```
🟡 Primary Yellow: #FFB703 (阳光黄 - 主要CTA按钮)
🔵 Ocean Blue: #219EBC (海洋蓝 - 信息和导航)
💙 Sky Blue: #8ECAE6 (天空蓝 - 次要元素)
🟠 Energy Orange: #FB8500 (活力橙 - 强调和警告)
🌸 Soft Pink: #FFDDD2 (柔和粉 - 背景和卡片)
```

**功能色板**
```
✅ Success Green: #06D6A0
❌ Error Red: #F72585
⚠️ Warning Amber: #F77F00
📝 Info Blue: #0077B6
🔘 Neutral Gray: #6C757D
```

**对比度要求**
- 所有文字与背景对比度 ≥ 4.5:1 (WCAG AA)
- 图标与背景对比度 ≥ 3:1
- 交互元素与背景对比度 ≥ 3:1

### 3. 排版系统

**字体选择**
```
主字体: Inter (拉丁) + Noto Sans SC (中文)
装饰字体: Rounded Mplus (友好圆润)
代码字体: JetBrains Mono
```

**字号体系** (所有字号符合1.25倍等比缩放)
```
📱 Mobile First
- Display: 32px (页面标题)
- H1: 28px (主标题)
- H2: 24px (副标题) 
- H3: 20px (章节标题)
- Body Large: 18px (重要正文)
- Body: 16px (标准正文) ← 最小字号
- Small: 14px (辅助文字，限制使用)

💻 Desktop
- Display: 48px
- H1: 40px
- H2: 32px
- H3: 28px
- Body Large: 20px
- Body: 18px
- Small: 16px
```

**行高 & 间距**
```
行高: 1.5 (正文), 1.2 (标题)
段落间距: 1em
列表项间距: 0.5em
```

### 4. 空间系统

**基础网格**: 8px
**间距比例**: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px

```
空间语义化:
- xs: 4px (紧密元素)
- sm: 8px (相关元素)  
- md: 12px (组件内间距)
- lg: 16px (组件间间距)
- xl: 24px (区块间距)
- 2xl: 32px (节点间距)
- 3xl: 48px (页面区域间距)
- 4xl: 64px (大型布局间距)
```

### 5. 圆角系统
```
- xs: 4px (小标签)
- sm: 6px (按钮、输入框)
- md: 8px (卡片)
- lg: 16px (主要容器) ← 推荐默认
- xl: 24px (模态框)
- 2xl: 32px (大型面板)
- full: 9999px (完全圆形)
```

### 6. 阴影系统
```
- 卡片阴影: 0 2px 8px rgba(0,0,0,0.1)
- 悬浮阴影: 0 4px 16px rgba(0,0,0,0.15)
- 模态阴影: 0 8px 32px rgba(0,0,0,0.2)
- 内阴影: inset 0 2px 4px rgba(0,0,0,0.1)
```

## 组件设计标准

### 按钮 Button
**尺寸规范**
```
Large: 48px 高度, 24px 内边距
Medium: 40px 高度, 20px 内边距  
Small: 32px 高度, 16px 内边距
```

**样式变体**
```
Primary: 黄色背景 + 深色文字 (主要行动)
Secondary: 蓝色边框 + 蓝色文字 (次要行动)
Ghost: 透明背景 + 彩色文字 (轻量行动)
Danger: 红色主题 (删除、重置)
```

**状态设计**
```
Default: 正常状态
Hover: 明度增加10%，添加投影
Active: 明度减少10%，内阴影
Disabled: 50%透明度，灰色
Loading: 旋转图标，禁用交互
```

### 卡片 Card
**基础结构**
```
容器: 白色背景，16px圆角，卡片阴影
内边距: 24px (desktop), 16px (mobile)
标题: H3字号，内容顶部
元数据: Small字号，浅色
行动区域: 右上角或底部
```

### 输入框 Input
**设计标准**
```
高度: 48px (触摸友好)
边框: 2px，默认灰色，聚焦时主色
圆角: 8px
内边距: 16px 水平，12px 垂直
字号: 16px (防止移动端缩放)
```

**状态样式**
```
Default: 浅灰边框
Focus: 主色边框 + 外发光
Error: 红色边框 + 错误图标
Success: 绿色边框 + 成功图标
Disabled: 浅灰背景 + 浅灰文字
```

### 导航 Navigation
**顶部导航栏**
```
高度: 64px
背景: 白色 + 轻微阴影
Logo: 左侧，28px 高度
主导航: 中间，水平布局
用户菜单: 右侧，头像 + 名字
```

**侧边导航 (如需要)**
```
宽度: 280px (desktop), 全屏覆盖 (mobile)
分组: 使用分割线和标题
当前页: 背景高亮 + 左侧色条
图标: 24px，统一风格
```

## 交互设计

### 动画原则
**持续时间**
```
微交互: 150ms (按钮点击)
小动画: 200-300ms (菜单展开)
页面转场: 400-500ms (路由切换)
```

**缓动函数**
```
ease-out: 用户触发的动作 (点击、拖拽)
ease-in-out: 系统触发的动作 (自动播放)
spring: 物理模拟 (弹性效果)
```

### 反馈设计
**成功反馈**
```
视觉: 绿色✓图标 + 明亮色彩
听觉: 轻快提示音 (可选)
触觉: 轻微震动 (移动端)
文案: "太棒了！" "成功啦！"
```

**错误处理**
```
语言: 儿童友好，避免技术术语
提示: "哎呀，出了点小问题"
建议: 提供具体解决方案
重试: 明显的重试按钮
```

## 可访问性

### 键盘导航
- Tab 顺序合理，跳转逻辑清晰
- 焦点可见，使用明显的焦点指示
- 快捷键提示，使用 aria-label

### 屏幕阅读器
- 所有图像有 alt 描述
- 按钮和链接有描述性文本
- 表单字段有 label 标签
- 页面结构使用语义化标签

### 色彩无障碍
- 不仅依赖颜色传达信息
- 提供文字或图标辅助
- 支持高对比度模式

## 游戏化元素

### 进度系统
```
经验值条: 渐变色彩，动画填充
等级徽章: 圆形图标，闪亮效果
成就解锁: 弹出动画，庆祝效果
```

### 每日任务
```
任务卡片: 明亮配色，进度条
完成标记: 大大的 ✓ 图标
奖励展示: 金币、星星动画
```

### 积分系统
```
积分显示: 顶部明显位置
获得动画: 数字跳跃，颜色变化
消费确认: 友好的确认对话框
```

## 内容策略

### 文案原则
```
1. 用简单词汇，避免专业术语
2. 句子简短，不超过15个字
3. 积极正面，鼓励探索
4. 添加适当emoji，增加趣味性
```

### 图标策略
```
风格: 线性图标，圆角设计
尺寸: 24px (标准), 16px (小), 32px (大)
颜色: 单色，与主题色协调
来源: Lucide React (统一风格)
```

### 插图使用
```
风格: 扁平插画，明亮色彩
主题: 友好角色，日常场景
位置: 空状态，引导页面
比例: 保持简洁，不喧宾夺主
```

## 响应式设计

### 断点系统
```
Mobile: 320px - 768px
Tablet: 768px - 1024px  
Desktop: 1024px+
```

### 移动端适配
```
触摸目标: 最小 44px x 44px
手势支持: 滑动、捏放缩放
键盘适配: 避免遮挡输入框
离线提示: 网络状态显示
```

### 平板端优化
```
双列布局: 充分利用横向空间
手势导航: 侧滑返回，双指操作
分屏适配: 支持多任务模式
```

## 性能考虑

### 图像优化
```
格式: WebP 优先，JPEG 备选
尺寸: 2x 分辨率适配高分屏
懒加载: 视窗外图像延迟加载
占位符: 模糊或骨架屏过渡
```

### 字体加载
```
预加载: 关键字体文件
回退: 系统字体作为备选
显示: font-display: swap
子集: 仅包含需要的字符
```

## 品牌体验

### 吉祥物设计
```
形象: 友好的AI机器人/动物
表情: 丰富的情感表达
互动: 引导用户，提供帮助
动画: 简单的波动、眨眼
```

### 声音设计 (可选)
```
成功音: 轻快铃声
错误音: 柔和提示  
点击音: 清脆反馈
背景音: 轻松氛围 (可关闭)
```

---

## 实施检查清单

### 开发阶段
- [ ] 建立设计系统代码库
- [ ] 配置 Tailwind 主题
- [ ] 创建 Storybook 文档
- [ ] 实现核心组件
- [ ] 移动端测试
- [ ] 可访问性审计

### 设计审查
- [ ] 儿童用户测试
- [ ] 教师反馈收集
- [ ] 家长可用性评估
- [ ] 色彩对比度检查
- [ ] 多设备兼容测试

### 持续优化
- [ ] 用户行为数据分析
- [ ] A/B 测试关键流程
- [ ] 定期设计系统更新
- [ ] 新功能设计规范