import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'ai-yellow': '#FFB703',
        'ai-blue': '#219EBC',
        'ai-orange': '#FB8500',
        'ai-pink': '#FFDDD2',
        'ai-sky': '#8ECAE6',
      },
      fontSize: {
        'child-xs': '14px',
        'child-sm': '16px',
        'child-base': '18px',
        'child-lg': '20px',
        'child-xl': '24px',
      },
      animation: {
        'bounce-gentle': 'bounceGentle 0.6s ease-in-out',
        'fade-in': 'fadeIn 0.3s ease-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-soft': 'pulseSoft 2s infinite',
      },
      keyframes: {
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-8px)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.7' },
        },
      },
      boxShadow: {
        'card': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'card-hover': '0 4px 16px rgba(0, 0, 0, 0.15)',
        'button': '0 2px 4px rgba(0, 0, 0, 0.1)',
      },
      backgroundImage: {
        'gradient-sky': 'linear-gradient(135deg, #8ECAE6 0%, #FFDDD2 100%)',
      },
    },
  },
  plugins: [],
}

export default config