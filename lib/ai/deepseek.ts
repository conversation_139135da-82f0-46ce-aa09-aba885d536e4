export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DeepSeekClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://api.deepseek.com/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async chat(messages: DeepSeekMessage[], options: {
    model?: string;
    temperature?: number;
    max_tokens?: number;
    safeMode?: boolean;
  } = {}): Promise<DeepSeekResponse> {
    const {
      model = 'deepseek-r1',
      temperature = 0.7,
      max_tokens = 1000,
      safeMode = true
    } = options;

    let processedMessages = messages;
    
    if (safeMode) {
      const safetyPrompt = "Please ensure your response is appropriate for elementary school students aged 8-12. Use simple language and avoid any inappropriate content.";
      processedMessages = [
        { role: 'system', content: safetyPrompt },
        ...messages
      ];
    }

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: processedMessages,
        temperature,
        max_tokens,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async simpleChat(userMessage: string, systemPrompt?: string): Promise<string> {
    const messages: DeepSeekMessage[] = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    
    messages.push({ role: 'user', content: userMessage });

    const response = await this.chat(messages);
    return response.choices[0]?.message?.content || '';
  }
}

export const deepSeekClient = new DeepSeekClient(
  process.env.DEEPSEEK_API_KEY || '',
  process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1'
);