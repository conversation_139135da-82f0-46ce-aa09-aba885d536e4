export interface JimengImageRequest {
  prompt: string;
  model?: string;
  size?: string;
  quality?: string;
  n?: number;
  response_format?: string;
}

export interface JimengVideoRequest {
  prompt: string;
  model?: string;
  size?: string;
  duration?: number;
  fps?: number;
}

export interface JimengImageResponse {
  data: Array<{
    url: string;
    b64_json?: string;
  }>;
}

export interface JimengVideoResponse {
  data: Array<{
    url: string;
    status: 'processing' | 'completed' | 'failed';
    task_id?: string;
  }>;
}

export class JimengClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://api.jimeng.ai/v1') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  private filterPrompt(prompt: string): string {
    const inappropriateWords = [
      'violence', 'blood', 'weapon', 'scary', 'horror', 'death', 'kill',
      'inappropriate', 'adult', 'sexual', 'nude', 'naked'
    ];
    
    const filteredPrompt = prompt.toLowerCase();
    
    for (const word of inappropriateWords) {
      if (filteredPrompt.includes(word)) {
        throw new Error(`Inappropriate content detected. Please use family-friendly prompts suitable for children.`);
      }
    }
    
    return `${prompt}. Make it colorful, fun, and suitable for children.`;
  }

  async generateImage(request: JimengImageRequest): Promise<JimengImageResponse> {
    const filteredPrompt = this.filterPrompt(request.prompt);
    
    const response = await fetch(`${this.baseUrl}/images/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: filteredPrompt,
        model: request.model || 'jimeng-image-v1',
        size: request.size || '1024x1024',
        quality: request.quality || 'standard',
        n: request.n || 1,
        response_format: request.response_format || 'url'
      })
    });

    if (!response.ok) {
      throw new Error(`Jimeng Image API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async generateVideo(request: JimengVideoRequest): Promise<JimengVideoResponse> {
    const filteredPrompt = this.filterPrompt(request.prompt);
    
    const response = await fetch(`${this.baseUrl}/videos/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: filteredPrompt,
        model: request.model || 'jimeng-video-v1',
        size: request.size || '1024x576',
        duration: request.duration || 5,
        fps: request.fps || 24
      })
    });

    if (!response.ok) {
      throw new Error(`Jimeng Video API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async checkVideoStatus(taskId: string): Promise<{ status: string; url?: string }> {
    const response = await fetch(`${this.baseUrl}/videos/status/${taskId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      }
    });

    if (!response.ok) {
      throw new Error(`Jimeng Video Status API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

export const jimengClient = new JimengClient(
  process.env.JIMENG_API_KEY || '',
  process.env.JIMENG_BASE_URL || 'https://api.jimeng.ai/v1'
);