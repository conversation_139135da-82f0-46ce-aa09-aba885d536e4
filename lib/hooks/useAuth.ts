'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export function useAuth(redirectTo: string = '/login') {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push(redirectTo);
    }
  }, [session, status, router, redirectTo]);

  return {
    session,
    isLoading: status === 'loading',
    isAuthenticated: !!session,
  };
}

export function useRequireAuth(redirectTo: string = '/login') {
  const auth = useAuth(redirectTo);
  
  if (auth.isLoading) {
    return { ...auth, user: null };
  }

  if (!auth.isAuthenticated) {
    return { ...auth, user: null };
  }

  return { ...auth, user: auth.session?.user };
}