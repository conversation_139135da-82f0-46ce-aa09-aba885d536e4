name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 9

    - name: Get pnpm store directory
      id: pnpm-cache
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

    - name: Setup pnpm cache
      uses: actions/cache@v3
      with:
        path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      run: pnpm install

    - name: Run linter
      run: pnpm lint

    - name: Run type check
      run: pnpm type-check

    - name: Build application
      run: pnpm build
      env:
        DATABASE_URL: postgresql://postgres:password@localhost:5432/test
        DEEPSEEK_API_KEY: test_key
        JIMENG_API_KEY: test_key
        NEXT_PUBLIC_APP_URL: http://localhost:3000
        NEXTAUTH_URL: http://localhost:3000
        NEXTAUTH_SECRET: test_secret