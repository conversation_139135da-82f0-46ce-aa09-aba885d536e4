# Memory - AI Fun Lab

This file contains persistent key-value information for the AI Fun Lab project. This follows Claude Code conventions for project memory management.

## Project Configuration
```
PROJECT_NAME=AI Fun Lab
PROJECT_TYPE=NextJS Educational Platform
STACK=Next.js 14 + TypeScript + Prisma + PostgreSQL
PACKAGE_MANAGER=pnpm
```

## AI Provider Configuration
```
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-r1
JIMENG_BASE_URL=https://api.jimeng.ai/v1
JIMENG_IMAGE_MODEL=jimeng-image-v1
JIMENG_VIDEO_MODEL=jimeng-video-v1
```

## Safety & Content Filtering
```
SAFE_MODE_PROMPT="Answer suitable for 8-12 y/o"
CONTENT_FILTER_ENABLED=true
MODERATION_LEVEL=strict
TARGET_AUDIENCE=elementary-school-students
```

## Database Configuration
```
DATABASE_TYPE=PostgreSQL
DATABASE_VERSION=16
ORM=Prisma
MAIN_MODELS=User,Artifact,Conversation
```

## Cloud Deployment Configuration
```
PRIMARY_CLOUD=Alibaba Cloud
ALI_REGION=cn-hangzhou
ALI_SERVICES=ECS,RDS,OSS
FALLBACK_CLOUD=Tencent Cloud
TENCENT_SERVICES=SCF,CVM,COS
```

## Development Environment
```
NODE_VERSION=>=20
PNPM_VERSION=>=9
DOCKER_COMPOSE_VERSION=>=2.0
TARGET_OS=macOS Sonoma
```

## API Endpoints Structure
```
CHAT_ENDPOINT=/api/deepseek/chat
IMAGE_ENDPOINT=/api/jimeng/image
VIDEO_ENDPOINT=/api/jimeng/video
```

## Key Architecture Decisions
```
ROUTING=App Router (Next.js 14)
STATE_MANAGEMENT=React Server Components + Client Components
AUTHENTICATION=NextAuth.js or custom JWT
STYLING=CSS Modules or Tailwind (TBD)
CI_CD=GitHub Actions
```

## Security & Compliance
```
COPPA_COMPLIANCE=required
CONTENT_MODERATION=multi-layer
API_RATE_LIMITING=enabled
SECRETS_MANAGEMENT=env-vars-only
```

## Performance Targets
```
PAGE_LOAD_TIME=<3s
IMAGE_GENERATION_TIME=<30s
VIDEO_GENERATION_TIME=<5min
UPTIME_TARGET=99.5%
```

## Development Workflow
```
GIT_WORKFLOW=feature-branch + PR
COMMIT_CONVENTION=conventional-commits
LINTING=ESLint + Prettier
PRE_COMMIT_HOOKS=Husky
MAIN_BRANCH=main
DEVELOPMENT_BRANCH=develop
```

## Environment Variables & Configuration

### Required API Keys
```
DEEPSEEK_API_KEY=sk-...
JIMENG_API_KEY=jimeng_...
NEXTAUTH_SECRET=random_32_char_string
NEXTAUTH_URL=http://localhost:3000
```

### Database Configuration
```
DATABASE_URL=postgresql://postgres:password@localhost:5432/aifunlab
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=aifunlab
```

### AI API Rate Limits & Costs
```
DEEPSEEK_CHAT_RATE_LIMIT=100_requests_per_minute
DEEPSEEK_COST_PER_1K_TOKENS=0.0014_USD
JIMENG_IMAGE_RATE_LIMIT=10_requests_per_minute
JIMENG_IMAGE_COST=0.05_USD_per_image
JIMENG_VIDEO_RATE_LIMIT=2_requests_per_minute
JIMENG_VIDEO_COST=0.20_USD_per_video
```

### User Tier Limits
```
STUDENT_DAILY_IMAGES=10
STUDENT_DAILY_VIDEOS=2
STUDENT_DAILY_CHAT=unlimited
TEACHER_DAILY_IMAGES=50
TEACHER_DAILY_VIDEOS=10
TEACHER_DAILY_CHAT=unlimited
```

### File Storage Configuration
```
STORAGE_TYPE=local|s3|oss|cos
LOCAL_STORAGE_PATH=./uploads
MAX_FILE_SIZE_MB=100
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,webp
ALLOWED_VIDEO_TYPES=mp4,webm
CDN_BASE_URL=https://cdn.aifunlab.com
```

### Cloud Deployment Constants
```
ALI_CLOUD_REGION=cn-hangzhou
ALI_ECS_INSTANCE_TYPE=ecs.g6.large
ALI_RDS_INSTANCE_TYPE=mysql.n2.medium.1
ALI_OSS_BUCKET=aifunlab-assets
TENCENT_CLOUD_REGION=ap-beijing
TENCENT_CVM_INSTANCE_TYPE=S5.LARGE8
TENCENT_COS_BUCKET=aifunlab-1234567890
```

### Security & Monitoring
```
SESSION_MAX_AGE=86400
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
CONTENT_SCAN_TIMEOUT_MS=5000
MODERATION_WEBHOOK_SECRET=webhook_secret_key
LOG_LEVEL=info
SENTRY_DSN=https://...
```

## Troubleshooting Constants

### Common Error Codes
```
AUTH_ERROR_INVALID_CREDENTIALS=4001
AUTH_ERROR_SESSION_EXPIRED=4002
AI_ERROR_RATE_LIMIT=4291
AI_ERROR_INAPPROPRIATE_CONTENT=4003
AI_ERROR_SERVICE_UNAVAILABLE=5001
DB_ERROR_CONNECTION=5002
STORAGE_ERROR_UPLOAD_FAILED=5003
```

### Default Prompts & Templates
```
SAFETY_SYSTEM_PROMPT="You are an AI assistant for elementary school students aged 8-12. Always provide safe, educational, and age-appropriate responses."
DEFAULT_IMAGE_STYLE="colorful, cartoon-style, child-friendly"
DEFAULT_VIDEO_DURATION=5
DEFAULT_VIDEO_FPS=24
CONTENT_FILTER_KEYWORDS="violence,blood,weapon,scary,horror,death,inappropriate,adult"
```

### Performance Thresholds
```
DATABASE_SLOW_QUERY_MS=1000
API_TIMEOUT_MS=30000
IMAGE_GENERATION_TIMEOUT_MS=30000
VIDEO_GENERATION_TIMEOUT_MS=300000
DASHBOARD_CACHE_TTL_SECONDS=300
```

## Development Environment Setup

### Local Development URLs
```
LOCAL_APP_URL=http://localhost:3000
LOCAL_API_BASE=/api
LOCAL_DB_URL=postgresql://postgres:password@localhost:5432/aifunlab
LOCAL_REDIS_URL=redis://localhost:6379
PRISMA_STUDIO_URL=http://localhost:5555
```

### Docker Configuration
```
DOCKER_COMPOSE_FILE=docker-compose.yml
POSTGRES_CONTAINER_NAME=learnai-db-1
WEB_CONTAINER_NAME=learnai-web-1
POSTGRES_PORT=5432
WEB_PORT=3000
```

### Build & Deployment
```
NODE_ENV=development|production
BUILD_OUTPUT=standalone
NEXT_TELEMETRY_DISABLED=1
DISABLE_ESLINT=false
ENABLE_SOURCE_MAPS=true
BUNDLE_ANALYZER=false
```