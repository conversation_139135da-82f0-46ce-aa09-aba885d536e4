'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';

export default function Navbar() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  
  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push('/');
  };

  const isActive = (path: string) => pathname === path;

  return (
    <nav 
      className={`navbar sticky top-0 z-50 transition-all duration-300 ${
        scrolled ? 'shadow-md bg-white/95 backdrop-blur-md' : 'bg-transparent'
      }`}
      aria-label="主导航"
    >
      <div className="container-wide">
        <div className="flex justify-between items-center h-16">
          {/* Logo - 按照设计指南28px高度 */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center gap-sm group" aria-label="AI Fun Lab 首页">
              <div 
                className="w-10 h-10 rounded-full flex items-center justify-center shadow-sm group-hover:scale-110 transition-transform animate-bounce-gentle"
                style={{backgroundColor: 'var(--primary-yellow)'}}
              >
                <span className="text-body-large">🎨</span>
              </div>
              <span className="logo text-body-large font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
                AI Fun Lab
              </span>
            </Link>
          </div>

          {/* 桌面端导航 - 水平布局 */}
          <div className="hidden md:flex items-center gap-md">
            {status === 'loading' ? (
              <div className="loading-spinner w-6 h-6 border-t-2 border-b-2 border-ocean-blue rounded-full animate-spin"></div>
            ) : session ? (
              <>
                {/* 主导航链接 */}
                <Link
                  href="/dashboard"
                  className={`nav-item ${isActive('/dashboard') ? 'nav-item-active' : ''}`}
                  aria-current={isActive('/dashboard') ? 'page' : undefined}
                >
                  <span className="nav-icon">🏠</span>
                  <span>工作台</span>
                </Link>
                <Link
                  href="/create-image"
                  className={`nav-item ${isActive('/create-image') ? 'nav-item-active' : ''}`}
                  aria-current={isActive('/create-image') ? 'page' : undefined}
                >
                  <span className="nav-icon">🎨</span>
                  <span>创作图片</span>
                </Link>
                <Link
                  href="/create-video"
                  className={`nav-item ${isActive('/create-video') ? 'nav-item-active' : ''}`}
                  aria-current={isActive('/create-video') ? 'page' : undefined}
                >
                  <span className="nav-icon">🎬</span>
                  <span>制作视频</span>
                </Link>
                <Link
                  href="/chat"
                  className={`nav-item ${isActive('/chat') ? 'nav-item-active' : ''}`}
                  aria-current={isActive('/chat') ? 'page' : undefined}
                >
                  <span className="nav-icon">💬</span>
                  <span>AI助手</span>
                </Link>
                
                {/* 用户菜单 - 右侧 */}
                <div className="flex items-center gap-md ml-lg pl-lg border-l border-gray-200">
                  <div className="flex items-center gap-sm">
                    <div className="avatar bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-md hover:shadow-lg transition-shadow">
                      {(session.user.name || session.user.email || 'U')[0].toUpperCase()}
                    </div>
                    <span className="text-small text-gray-700 font-medium hidden lg:block">
                      {session.user.name || session.user.email}
                    </span>
                  </div>
                  <button
                    onClick={handleSignOut}
                    className="btn btn-ghost btn-small hover:bg-red-50 hover:text-red-600 transition-colors"
                    aria-label="退出登录"
                  >
                    <span className="nav-icon">🚪</span>
                    <span>退出</span>
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link href="/login" className="btn btn-ghost btn-medium hover:bg-gray-50 transition-colors">
                  <span className="nav-icon">🔑</span>
                  <span>登录</span>
                </Link>
                <Link href="/register" className="btn btn-primary btn-medium hover:brightness-105 hover:shadow-md transition-all">
                  <span className="nav-icon">🌟</span>
                  <span>注册</span>
                </Link>
              </>
            )}
          </div>

          {/* 移动端菜单按钮 */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="btn btn-ghost btn-small p-sm touch-target"
              aria-expanded={isMobileMenuOpen}
              aria-label={isMobileMenuOpen ? '关闭菜单' : '打开菜单'}
            >
              {isMobileMenuOpen ? (
                <span className="text-body-large">✕</span>
              ) : (
                <span className="text-body-large">☰</span>
              )}
            </button>
          </div>
        </div>

          {/* 移动端菜单 - 全屏覆盖 */}
          {isMobileMenuOpen && (
            <div className="md:hidden fixed inset-0 z-50 bg-white animate-fade-in">
              <div className="container-full p-lg pt-16 pb-lg h-full overflow-y-auto">
                {status === 'loading' ? (
                  <div className="flex justify-center items-center h-full">
                    <div className="loading-spinner w-8 h-8 border-t-2 border-b-2 border-ocean-blue rounded-full animate-spin"></div>
                  </div>
                ) : session ? (
                  <>
                    {/* 用户信息 */}
                    <div className="flex items-center gap-md p-md rounded-lg mb-lg shadow-sm animate-slide-up"
                         style={{backgroundColor: 'var(--soft-pink)'}}>
                      <div className="avatar w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-md text-h3">
                        {(session.user.name || session.user.email || 'U')[0].toUpperCase()}
                      </div>
                      <div>
                        <p className="text-body font-medium text-gray-900">
                          {session.user.name || session.user.email}
                        </p>
                        <p className="text-small text-gray-500">AI Fun Lab 小创作家</p>
                      </div>
                    </div>

                    {/* 导航链接 */}
                    <div className="space-y-sm animate-slide-up" style={{animationDelay: '50ms'}}>
                      <Link
                    href="/dashboard"
                    className={`block p-md rounded-lg text-body font-medium transition-colors flex items-center gap-sm touch-target ${
                      isActive('/dashboard') 
                        ? 'bg-blue-50 text-blue-600 shadow-sm' 
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                    aria-current={isActive('/dashboard') ? 'page' : undefined}
                  >
                    <span className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">🏠</span>
                    <span>工作台</span>
                  </Link>
                      <Link
                        href="/create-image"
                        className={`block p-md rounded-lg text-body font-medium transition-colors flex items-center gap-sm touch-target ${
                          isActive('/create-image') 
                            ? 'bg-blue-50 text-blue-600 shadow-sm' 
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                        aria-current={isActive('/create-image') ? 'page' : undefined}
                      >
                        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">🎨</span>
                        <span>创作图片</span>
                      </Link>
                      <Link
                        href="/create-video"
                        className={`block p-md rounded-lg text-body font-medium transition-colors flex items-center gap-sm touch-target ${
                          isActive('/create-video') 
                            ? 'bg-blue-50 text-blue-600 shadow-sm' 
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                        aria-current={isActive('/create-video') ? 'page' : undefined}
                      >
                        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-sky-100 text-sky-600">🎬</span>
                        <span>制作视频</span>
                      </Link>
                      <Link
                        href="/chat"
                        className={`block p-md rounded-lg text-body font-medium transition-colors flex items-center gap-sm touch-target ${
                          isActive('/chat') 
                            ? 'bg-blue-50 text-blue-600 shadow-sm' 
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                        aria-current={isActive('/chat') ? 'page' : undefined}
                      >
                        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-green-100 text-green-600">💬</span>
                        <span>AI助手</span>
                      </Link>
                    </div>

                    {/* 退出登录 */}
                    <div className="pt-lg border-t border-gray-200 mt-lg animate-slide-up" style={{animationDelay: '150ms'}}>
                      <button
                        onClick={() => {
                          handleSignOut();
                          setIsMobileMenuOpen(false);
                        }}
                        className="w-full text-left p-md rounded-lg text-body font-medium text-red-600 hover:bg-red-50 transition-colors flex items-center gap-sm touch-target"
                        aria-label="退出登录"
                      >
                        <span className="w-8 h-8 flex items-center justify-center rounded-full bg-red-100 text-red-600">🚪</span>
                        <span>退出登录</span>
                      </button>
                    </div>
                </>
              ) : (
                <>
                  <div className="space-y-sm animate-slide-up" style={{animationDelay: '50ms'}}>
                    <Link
                      href="/login"
                      className="block p-md rounded-lg text-body font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors flex items-center gap-sm touch-target"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-600">🔑</span>
                      <span>登录</span>
                    </Link>
                    <Link
                      href="/register"
                      className="block p-md rounded-lg text-body font-medium hover:opacity-90 transition-opacity flex items-center gap-sm touch-target shadow-sm hover:shadow-md"
                      style={{backgroundColor: 'var(--primary-yellow)', color: '#1f2937'}}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="w-8 h-8 flex items-center justify-center rounded-full bg-white/80 text-yellow-600">🌟</span>
                      <span>注册</span>
                    </Link>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}