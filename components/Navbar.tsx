'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useState } from 'react';

export default function Navbar() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push('/');
  };

  const isActive = (path: string) => pathname === path;

  return (
    <nav className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50">
      <div className="container-wide">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="w-10 h-10 bg-gradient-fun rounded-lg flex items-center justify-center shadow-md group-hover:scale-105 transition-transform">
                <span className="text-xl">🎨</span>
              </div>
              <span className="text-child-lg font-bold text-gray-900 group-hover:text-indigo-600 transition-colors">
                AI Fun Lab
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {status === 'loading' ? (
              <div className="skeleton w-32 h-8"></div>
            ) : session ? (
              <>
                <Link
                  href="/dashboard"
                  className={`nav-link ${isActive('/dashboard') ? 'nav-link-active' : ''}`}
                >
                  🏠 工作台
                </Link>
                <Link
                  href="/create-image"
                  className={`nav-link ${isActive('/create-image') ? 'nav-link-active' : ''}`}
                >
                  🎨 创作图片
                </Link>
                <Link
                  href="/create-video"
                  className={`nav-link ${isActive('/create-video') ? 'nav-link-active' : ''}`}
                >
                  🎬 制作视频
                </Link>
                <Link
                  href="/chat"
                  className={`nav-link ${isActive('/chat') ? 'nav-link-active' : ''}`}
                >
                  💬 AI助手
                </Link>
                
                {/* User Menu */}
                <div className="flex items-center space-x-3 ml-4 pl-4 border-l border-gray-200">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-semibold">
                      {(session.user.name || session.user.email || 'U')[0].toUpperCase()}
                    </div>
                    <span className="text-child-sm text-gray-700 font-medium hidden lg:block">
                      {session.user.name || session.user.email}
                    </span>
                  </div>
                  <button
                    onClick={handleSignOut}
                    className="btn btn-ghost btn-small"
                  >
                    🚪 退出
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link href="/login" className="btn btn-ghost">
                  🔑 登录
                </Link>
                <Link href="/register" className="btn btn-primary">
                  🌟 注册
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="btn btn-ghost btn-small p-2"
            >
              {isMobileMenuOpen ? (
                <span className="text-xl">✕</span>
              ) : (
                <span className="text-xl">☰</span>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-100 py-4 animate-slide-up">
            <div className="space-y-2">
              {session ? (
                <>
                  {/* User Info */}
                  <div className="flex items-center space-x-3 px-3 py-2 bg-gray-50 rounded-lg mb-4">
                    <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-white font-semibold">
                      {(session.user.name || session.user.email || 'U')[0].toUpperCase()}
                    </div>
                    <div>
                      <p className="text-child-sm font-medium text-gray-900">
                        {session.user.name || session.user.email}
                      </p>
                      <p className="text-xs text-gray-500">AI Fun Lab 用户</p>
                    </div>
                  </div>

                  {/* Navigation Links */}
                  <Link
                    href="/dashboard"
                    className={`block px-3 py-2 rounded-md text-child-sm font-medium ${
                      isActive('/dashboard') 
                        ? 'bg-indigo-50 text-indigo-600' 
                        : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    🏠 工作台
                  </Link>
                  <Link
                    href="/create-image"
                    className={`block px-3 py-2 rounded-md text-child-sm font-medium ${
                      isActive('/create-image') 
                        ? 'bg-indigo-50 text-indigo-600' 
                        : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    🎨 创作图片
                  </Link>
                  <Link
                    href="/create-video"
                    className={`block px-3 py-2 rounded-md text-child-sm font-medium ${
                      isActive('/create-video') 
                        ? 'bg-indigo-50 text-indigo-600' 
                        : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    🎬 制作视频
                  </Link>
                  <Link
                    href="/chat"
                    className={`block px-3 py-2 rounded-md text-child-sm font-medium ${
                      isActive('/chat') 
                        ? 'bg-indigo-50 text-indigo-600' 
                        : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    💬 AI助手
                  </Link>

                  {/* Sign Out */}
                  <div className="pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        handleSignOut();
                        setIsMobileMenuOpen(false);
                      }}
                      className="w-full text-left px-3 py-2 rounded-md text-child-sm font-medium text-red-600 hover:bg-red-50"
                    >
                      🚪 退出登录
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block px-3 py-2 rounded-md text-child-sm font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    🔑 登录
                  </Link>
                  <Link
                    href="/register"
                    className="block px-3 py-2 rounded-md text-child-sm font-medium bg-indigo-600 text-white hover:bg-indigo-700"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    🌟 注册
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}