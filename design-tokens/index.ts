// AI Fun Lab - Design Tokens
// Central export for all design tokens

export { colors, tailwindColors, type ColorPalette, type TailwindColors } from './colors';
export { typography, tailwindTypography, type Typography, type TailwindTypography } from './typography';
export { spacing, tailwindSpacing, tailwindRadius, type Spacing, type TailwindSpacing } from './spacing';

// Import for local use
import { colors, tailwindColors } from './colors';
import { typography, tailwindTypography } from './typography';
import { spacing, tailwindSpacing, tailwindRadius } from './spacing';

// Combined design system
export const designSystem = {
  colors,
  typography, 
  spacing,
} as const;

// Tailwind config helpers
export const tailwindTokens = {
  colors: tailwindColors,
  fontFamily: tailwindTypography.fontFamily,
  fontSize: tailwindTypography.fontSize,
  fontWeight: tailwindTypography.fontWeight,
  lineHeight: tailwindTypography.lineHeight,
  letterSpacing: tailwindTypography.letterSpacing,
  spacing: tailwindSpacing,
  borderRadius: tailwindRadius,
} as const;

export type DesignSystem = typeof designSystem;