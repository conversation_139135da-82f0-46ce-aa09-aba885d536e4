// AI Fun Lab - Color Design Tokens
// Designed for kids 6-11 with high contrast and friendly colors

export const colors = {
  // Primary Brand Colors
  primary: {
    yellow: '#FFB703',    // 阳光黄 - Main CTA buttons
    blue: '#219EBC',      // 海洋蓝 - Navigation and info
    skyBlue: '#8ECAE6',   // 天空蓝 - Secondary elements
    orange: '#FB8500',    // 活力橙 - Emphasis and warnings
    pink: '#FFDDD2',      // 柔和粉 - Backgrounds and cards
  },

  // Functional Colors
  success: '#06D6A0',     // ✅ Success Green
  error: '#F72585',       // ❌ Error Red  
  warning: '#F77F00',     // ⚠️ Warning Amber
  info: '#0077B6',        // 📝 Info Blue
  neutral: '#6C757D',     // 🔘 Neutral Gray

  // Extended Palette for UI
  gray: {
    50: '#F8F9FA',
    100: '#F1F3F4',
    200: '#E9ECEF',
    300: '#DEE2E6',
    400: '#CED4DA',
    500: '#ADB5BD',
    600: '#6C757D',
    700: '#495057',
    800: '#343A40',
    900: '#212529',
  },

  // Background Colors
  background: {
    primary: '#FFFFFF',
    secondary: '#FFDDD2',  // Soft pink
    accent: '#F8F9FA',     // Light gray
    gradient: 'linear-gradient(135deg, #8ECAE6 0%, #FFDDD2 100%)',
  },

  // Text Colors (High contrast for readability)
  text: {
    primary: '#212529',    // Dark gray for main text
    secondary: '#495057',  // Medium gray for secondary text
    muted: '#6C757D',      // Light gray for muted text
    inverse: '#FFFFFF',    // White text for dark backgrounds
    accent: '#219EBC',     // Brand blue for links and accents
  },

  // Border Colors
  border: {
    default: '#E9ECEF',
    focus: '#219EBC',
    error: '#F72585',
    success: '#06D6A0',
  },

  // Shadow Colors
  shadow: {
    sm: 'rgba(0, 0, 0, 0.05)',
    md: 'rgba(0, 0, 0, 0.1)',
    lg: 'rgba(0, 0, 0, 0.15)',
    xl: 'rgba(0, 0, 0, 0.2)',
  }
} as const;

// Tailwind-compatible color object
export const tailwindColors = {
  primary: colors.primary.yellow,
  secondary: colors.primary.blue,
  accent: colors.primary.orange,
  success: colors.success,
  error: colors.error,
  warning: colors.warning,
  info: colors.info,
  
  // Custom color palette
  'ai-yellow': colors.primary.yellow,
  'ai-blue': colors.primary.blue,
  'ai-sky': colors.primary.skyBlue,
  'ai-orange': colors.primary.orange,
  'ai-pink': colors.primary.pink,
  
  gray: colors.gray,
} as const;

export type ColorPalette = typeof colors;
export type TailwindColors = typeof tailwindColors;