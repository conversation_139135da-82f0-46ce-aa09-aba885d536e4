// AI Fun Lab - Typography Design Tokens
// Child-friendly typography with minimum 16px font size

export const typography = {
  // Font Families
  fontFamily: {
    primary: ['Inter', 'system-ui', 'sans-serif'],
    chinese: ['Noto Sans SC', 'system-ui', 'sans-serif'],
    rounded: ['Rounded Mplus', 'Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'Consolas', 'monospace'],
  },

  // Font Sizes (Mobile First - 1.25x ratio)
  fontSize: {
    // Mobile
    mobile: {
      xs: '14px',      // Limited use only
      sm: '16px',      // Minimum body text
      base: '18px',    // Standard body text
      lg: '20px',      // Section titles
      xl: '24px',      // Subsection titles  
      '2xl': '28px',   // Page titles
      '3xl': '32px',   // Display titles
    },
    
    // Desktop
    desktop: {
      xs: '16px',      // Small text
      sm: '18px',      // Body text
      base: '20px',    // Large body text
      lg: '28px',      // Section titles
      xl: '32px',      // Subsection titles
      '2xl': '40px',   // Page titles
      '3xl': '48px',   // Display titles
    }
  },

  // Line Heights
  lineHeight: {
    tight: '1.2',    // For headings
    normal: '1.5',   // For body text
    relaxed: '1.6',  // For long-form content
  },

  // Font Weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  // Letter Spacing
  letterSpacing: {
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
  },

  // Text Styles (Semantic)
  textStyles: {
    // Display text (Hero titles)
    display: {
      mobile: {
        fontSize: '32px',
        lineHeight: '1.2',
        fontWeight: '700',
        letterSpacing: '-0.025em',
      },
      desktop: {
        fontSize: '48px',
        lineHeight: '1.2', 
        fontWeight: '700',
        letterSpacing: '-0.025em',
      }
    },

    // Headings
    h1: {
      mobile: {
        fontSize: '28px',
        lineHeight: '1.2',
        fontWeight: '600',
      },
      desktop: {
        fontSize: '40px',
        lineHeight: '1.2',
        fontWeight: '600',
      }
    },

    h2: {
      mobile: {
        fontSize: '24px',
        lineHeight: '1.3',
        fontWeight: '600',
      },
      desktop: {
        fontSize: '32px',
        lineHeight: '1.3',
        fontWeight: '600',
      }
    },

    h3: {
      mobile: {
        fontSize: '20px',
        lineHeight: '1.4',
        fontWeight: '600',
      },
      desktop: {
        fontSize: '28px',
        lineHeight: '1.4',
        fontWeight: '600',
      }
    },

    // Body text
    bodyLarge: {
      mobile: {
        fontSize: '18px',
        lineHeight: '1.5',
        fontWeight: '400',
      },
      desktop: {
        fontSize: '20px',
        lineHeight: '1.5',
        fontWeight: '400',
      }
    },

    body: {
      mobile: {
        fontSize: '16px',  // Minimum for accessibility
        lineHeight: '1.5',
        fontWeight: '400',
      },
      desktop: {
        fontSize: '18px',
        lineHeight: '1.5',
        fontWeight: '400',
      }
    },

    // UI elements
    button: {
      mobile: {
        fontSize: '16px',
        lineHeight: '1.2',
        fontWeight: '500',
      },
      desktop: {
        fontSize: '18px',
        lineHeight: '1.2',
        fontWeight: '500',
      }
    },

    caption: {
      mobile: {
        fontSize: '14px',  // Use sparingly
        lineHeight: '1.4',
        fontWeight: '400',
      },
      desktop: {
        fontSize: '16px',
        lineHeight: '1.4',
        fontWeight: '400',
      }
    },
  }
} as const;

// Tailwind-compatible typography
export const tailwindTypography = {
  fontFamily: {
    sans: typography.fontFamily.primary,
    'sans-cn': typography.fontFamily.chinese,
    rounded: typography.fontFamily.rounded,
    mono: typography.fontFamily.mono,
  },
  
  fontSize: {
    xs: ['14px', '1.4'],
    sm: ['16px', '1.5'],   // Minimum size
    base: ['18px', '1.5'],
    lg: ['20px', '1.5'],
    xl: ['24px', '1.4'],
    '2xl': ['28px', '1.3'],
    '3xl': ['32px', '1.2'],
    '4xl': ['40px', '1.2'],
    '5xl': ['48px', '1.2'],
  },

  fontWeight: typography.fontWeight,
  lineHeight: typography.lineHeight,
  letterSpacing: typography.letterSpacing,
} as const;

export type Typography = typeof typography;
export type TailwindTypography = typeof tailwindTypography;