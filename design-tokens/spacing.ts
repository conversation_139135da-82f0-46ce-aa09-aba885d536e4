// AI Fun Lab - Spacing Design Tokens
// 8px base grid system for consistent spacing

export const spacing = {
  // Base grid: 8px
  base: 8,

  // Spacing scale
  scale: {
    0: '0',
    1: '4px',    // xs - Tight elements
    2: '8px',    // sm - Related elements  
    3: '12px',   // md - Component internal spacing
    4: '16px',   // lg - Component spacing (recommended default)
    5: '20px',   // Between components
    6: '24px',   // xl - Block spacing
    8: '32px',   // 2xl - Section spacing
    10: '40px',  // Large section spacing
    12: '48px',  // 3xl - Page area spacing
    16: '64px',  // 4xl - Major layout spacing
    20: '80px',  // Hero spacing
    24: '96px',  // Extra large spacing
  },

  // Semantic spacing
  semantic: {
    // Component internal spacing
    elementGap: '4px',      // Between tightly related elements
    componentPadding: '16px', // Default component padding
    cardPadding: '24px',    // Card internal spacing
    
    // Layout spacing  
    sectionGap: '32px',     // Between page sections
    layoutGap: '48px',      // Between major layout areas
    pageMargin: '16px',     // Page edge margins (mobile)
    pageMarginDesktop: '24px', // Page edge margins (desktop)

    // Interactive elements
    buttonPadding: '16px 24px',   // Button internal spacing
    inputPadding: '12px 16px',    // Input field padding
    iconMargin: '8px',            // Space around icons
    
    // Lists and content
    listGap: '8px',         // Between list items
    paragraphGap: '16px',   // Between paragraphs
    headingMargin: '24px 0 16px 0', // Heading margins
  },

  // Border radius scale
  radius: {
    none: '0',
    xs: '4px',     // Small tags
    sm: '6px',     // Buttons, inputs
    md: '8px',     // Cards
    lg: '16px',    // Main containers (recommended)
    xl: '24px',    // Modals
    '2xl': '32px', // Large panels
    full: '9999px', // Fully rounded
  },

  // Container sizes
  container: {
    xs: '320px',
    sm: '640px', 
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Touch targets (minimum 44px for accessibility)
  touchTarget: {
    minimum: '44px',    // WCAG minimum
    comfortable: '48px', // Recommended size
    large: '56px',      // Large buttons
  },
} as const;

// Tailwind-compatible spacing
export const tailwindSpacing = {
  // Override default Tailwind spacing with our 8px grid
  0: spacing.scale[0],
  1: spacing.scale[1],   // 4px
  2: spacing.scale[2],   // 8px  
  3: spacing.scale[3],   // 12px
  4: spacing.scale[4],   // 16px
  5: spacing.scale[5],   // 20px
  6: spacing.scale[6],   // 24px
  8: spacing.scale[8],   // 32px
  10: spacing.scale[10], // 40px
  12: spacing.scale[12], // 48px
  16: spacing.scale[16], // 64px
  20: spacing.scale[20], // 80px
  24: spacing.scale[24], // 96px

  // Semantic aliases
  'component': spacing.scale[4],     // 16px
  'section': spacing.scale[8],       // 32px
  'layout': spacing.scale[12],       // 48px
  'touch': spacing.touchTarget.comfortable, // 48px
} as const;

// Border radius for Tailwind
export const tailwindRadius = {
  none: spacing.radius.none,
  sm: spacing.radius.sm,
  DEFAULT: spacing.radius.lg,  // 16px default
  md: spacing.radius.md,
  lg: spacing.radius.lg,
  xl: spacing.radius.xl,
  '2xl': spacing.radius['2xl'],
  full: spacing.radius.full,
} as const;

export type Spacing = typeof spacing;
export type TailwindSpacing = typeof tailwindSpacing;