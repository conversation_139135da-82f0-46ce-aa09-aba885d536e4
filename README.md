# AI Fun Lab

A public website that lets elementary-school students register, use Chinese AI models to generate images/videos/chat, and stores every artifact & conversation for later review.

## Tech Stack

- **Frontend**: Next.js 14 (App Router, TypeScript)
- **Database**: Prisma ORM + PostgreSQL
- **Package Manager**: pnpm
- **AI Providers**: 
  - DeepSeek R1 (Chat) - OpenAI-compatible API
  - Jimeng (Image & Video Generation)
- **Deployment**: Docker + Alibaba Cloud ECS/RDS + Tencent Cloud SCF

## Features

- **Image Generation**: Text-to-image using Jimeng AI
- **Video Generation**: Text-to-video using Jimeng AI
- **AI Chat**: Educational chat using DeepSeek R1
- **User Management**: Registration and authentication
- **Content Safety**: Multi-layer filtering for child-appropriate content
- **Artifact Storage**: All generated content stored for review

## Local Development Setup (macOS)

### Prerequisites

- Node.js ≥ 20
- pnpm ≥ 9
- Docker Desktop
- PostgreSQL (via Docker)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LearnAI
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your API keys (see [Environment Setup](#environment-setup) for detailed instructions):
   ```bash
   # AI APIs (Required for functionality)
   DEEPSEEK_API_KEY=your_deepseek_api_key_here
   JIMENG_API_KEY=your_jimeng_api_key_here
   
   # Authentication (Required for login)
   NEXTAUTH_SECRET=your_secure_random_string_here
   NEXTAUTH_URL=http://localhost:3000
   
   # Database (Already configured for Docker)
   DATABASE_URL="postgresql://postgres:password@localhost:5432/aifunlab"
   ```

4. **Start the database**
   ```bash
   docker-compose up -d db
   ```

5. **Run database migrations**
   ```bash
   pnpm dlx prisma migrate dev --name init
   ```

6. **Generate Prisma client**
   ```bash
   pnpm dlx prisma generate
   ```

7. **Start the development server**
   ```bash
   pnpm dev
   ```

8. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Quick Start (每次重启电脑后)

如果你已经完成了初始安装，每次重启电脑后只需要运行：

```bash
# 1. 启动数据库 (需要 Docker Desktop 运行)
docker compose up -d db

# 2. 启动开发服务器
pnpm dev

# 3. 打开浏览器访问 http://localhost:3000
```

**注意事项**：
- 确保 Docker Desktop 已启动
- 数据库配置在 `docker-compose.yml` 中已设置好用户名密码 (`postgres:password`)
- 如果遇到数据库连接问题，等待 10-15 秒让 PostgreSQL 完全启动
- 你的数据会持久化保存，不会因为重启而丢失

### Database Management

- **View database**: `pnpm dlx prisma studio`
- **Reset database**: `pnpm dlx prisma migrate reset`
- **Apply migrations**: `pnpm dlx prisma migrate dev`
- **Check database status**: `docker compose ps`
- **Stop database**: `docker compose down`
- **View database logs**: `docker compose logs db`

### 数据库连接信息

你的本地 PostgreSQL 配置已经在 Docker 中设置好了：
- **Host**: localhost
- **Port**: 5432
- **Database**: aifunlab
- **Username**: postgres
- **Password**: password
- **Connection String**: `postgresql://postgres:password@localhost:5432/aifunlab`

这些配置在 `docker-compose.yml` 和 `.env` 文件中已经设置好，你不需要手动配置 PostgreSQL。

## Environment Setup

### Getting API Keys

#### DeepSeek API Key
1. Visit [DeepSeek Console](https://platform.deepseek.com)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (starts with `sk-`)

**Cost**: ~$0.0014 per 1K tokens (very affordable for development)

#### Jimeng API Key  
1. Visit [Jimeng AI Platform](https://platform.jimeng.ai)
2. Register and verify your account
3. Go to API Management
4. Generate new API key
5. Copy the key (starts with `jimeng_`)

**Cost**: ~$0.05 per image, ~$0.20 per video

#### NextAuth Secret
Generate a secure random string for authentication:
```bash
# Option 1: Use OpenSSL (recommended)
openssl rand -base64 32

# Option 2: Use online generator
# Visit https://generate-secret.vercel.app/32

# Option 3: Use Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### Complete .env Configuration

```bash
# Copy and customize this template
cp .env.example .env

# Edit .env with your actual values:
# AI API Keys (Required for functionality)
DEEPSEEK_API_KEY=sk-1234567890abcdef...
JIMENG_API_KEY=jimeng_9876543210fedcba...

# Authentication (Required)
NEXTAUTH_SECRET=your-32-character-random-string
NEXTAUTH_URL=http://localhost:3000

# Database (Pre-configured for Docker)
DATABASE_URL="postgresql://postgres:password@localhost:5432/aifunlab"

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: Development Features
NODE_ENV=development
LOG_LEVEL=info
ENABLE_DEBUG=true
```

### Environment Validation

Test your configuration:
```bash
# Check if environment variables are loaded
pnpm dlx next info

# Test database connection
pnpm dlx prisma db push

# Test API keys (requires running app)
curl -X POST http://localhost:3000/api/deepseek/chat \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Hello"}]}'
```

## Troubleshooting

### Common Issues

#### 1. Docker Issues

**Problem**: `docker: command not found`
```bash
# Solution: Install Docker Desktop
# Download from https://www.docker.com/products/docker-desktop
# Make sure Docker Desktop is running
```

**Problem**: Database connection failed
```bash
# Check if database is running
docker compose ps

# Restart database
docker compose down && docker compose up -d db

# Check database logs
docker compose logs db
```

**Problem**: Port 5432 already in use
```bash
# Find what's using the port
lsof -i :5432

# Kill the process or change Docker port in docker-compose.yml
```

#### 2. pnpm Issues

**Problem**: `pnpm: command not found`
```bash
# Install pnpm
curl -fsSL https://get.pnpm.io/install.sh | sh -
source ~/.zshrc  # or restart terminal
```

**Problem**: Permission denied during pnpm install
```bash
# Fix npm permissions (for global installs)
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
export PATH=~/.npm-global/bin:$PATH
```

#### 3. Environment Variable Issues

**Problem**: API calls return 401 Unauthorized
```bash
# Check if API keys are set
echo $DEEPSEEK_API_KEY
echo $JIMENG_API_KEY

# Restart development server after changing .env
pnpm dev
```

**Problem**: NextAuth configuration error
```bash
# Generate new secret
openssl rand -base64 32

# Make sure NEXTAUTH_URL matches your app URL
# For production: https://yourdomain.com
# For development: http://localhost:3000
```

#### 4. Database Issues

**Problem**: Prisma client out of sync
```bash
# Regenerate Prisma client
pnpm dlx prisma generate

# Reset database (WARNING: deletes all data)
pnpm dlx prisma migrate reset
```

**Problem**: Database schema changes not applied
```bash
# Create and apply migration
pnpm dlx prisma migrate dev --name your_migration_name

# Force push schema (development only)
pnpm dlx prisma db push
```

#### 5. Build Issues

**Problem**: TypeScript compilation errors
```bash
# Check for type errors
pnpm type-check

# Update dependencies
pnpm update

# Clear Next.js cache
rm -rf .next
pnpm build
```

**Problem**: Out of memory during build
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
pnpm build
```

### Performance Issues

#### Slow Database Queries
```bash
# Enable query logging
# Add to .env:
# DATABASE_URL="postgresql://postgres:password@localhost:5432/aifunlab?logging=true"

# Check slow queries in logs
docker compose logs db | grep "slow query"
```

#### High Memory Usage
```bash
# Monitor resource usage
docker stats

# Restart services
docker compose restart
```

### Development Tips

#### Reset Everything
```bash
# Nuclear option: reset entire development environment
docker compose down -v  # Removes volumes (deletes data!)
rm -rf node_modules .next
pnpm install
docker compose up -d db
pnpm dlx prisma migrate reset --force
pnpm dev
```

#### View Database Contents
```bash
# Open Prisma Studio
pnpm dlx prisma studio
# Opens at http://localhost:5555
```

#### Debug API Calls
```bash
# Enable API logging
# Add to .env: LOG_LEVEL=debug

# Test endpoints directly
curl -X POST http://localhost:3000/api/deepseek/chat \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Test message"}]}'
```

### Getting Help

If you're still having issues:

1. **Check the logs**: `docker compose logs` and browser console
2. **Verify environment**: Compare your `.env` with `.env.example`
3. **Update dependencies**: `pnpm update`
4. **Check GitHub Issues**: Search for similar problems
5. **Community Support**: Post questions with error details

### Docker Development

Run the entire stack with Docker:

```bash
# Build and start all services
docker-compose up --build

# Stop all services
docker-compose down

# View logs
docker-compose logs -f web
```

## API Endpoints

- `POST /api/deepseek/chat` - Chat with DeepSeek R1
- `POST /api/jimeng/image` - Generate images
- `POST /api/jimeng/video` - Generate videos
- `GET /api/jimeng/video?taskId=<id>` - Check video status

## Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   │   ├── deepseek/      # DeepSeek chat endpoints
│   │   └── jimeng/        # Jimeng image/video endpoints
│   ├── dashboard/         # User dashboard
│   ├── login/             # Authentication
│   └── create-image/      # Image generation UI
├── lib/                   # Shared utilities
│   └── ai/               # AI provider adapters
├── prisma/               # Database schema and migrations
├── docker-compose.yml    # Docker services
└── Dockerfile           # Container configuration
```

## Alibaba Cloud Deployment

### Prerequisites

- Alibaba Cloud account
- ECS instance (Ubuntu 20.04+ recommended)
- RDS PostgreSQL instance
- OSS bucket for file storage

### Setup Steps

1. **Create ECS Instance**
   - Instance Type: ecs.g6.large or higher
   - Image: Ubuntu 20.04 LTS
   - Security Group: Allow HTTP (80), HTTPS (443), SSH (22)

2. **Install Docker on ECS**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Install Docker Compose
   sudo apt install docker-compose -y
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   ```

3. **Configure RDS PostgreSQL**
   - Create RDS instance in the same region
   - Configure security group to allow ECS access
   - Note connection details for environment variables

4. **Deploy Application**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd LearnAI
   
   # Set production environment variables
   cp .env.example .env.production
   # Edit .env.production with production values
   
   # Deploy with Docker Compose
   docker-compose -f docker-compose.prod.yml up -d
   ```

5. **Configure Nginx (Optional)**
   ```bash
   # Install Nginx
   sudo apt install nginx -y
   
   # Configure reverse proxy
   sudo nano /etc/nginx/sites-available/aifunlab
   
   # Enable site
   sudo ln -s /etc/nginx/sites-available/aifunlab /etc/nginx/sites-enabled/
   sudo nginx -t && sudo systemctl reload nginx
   ```

### Environment Variables for Production

```bash
# Database (use RDS endpoint)
DATABASE_URL="************************************************/aifunlab"

# AI APIs
DEEPSEEK_API_KEY=your_production_deepseek_key
JIMENG_API_KEY=your_production_jimeng_key

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_secure_secret

# Alibaba Cloud
ALIBABA_CLOUD_ACCESS_KEY_ID=your_access_key
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_secret_key
ALIBABA_CLOUD_REGION=cn-hangzhou
```

## Tencent Cloud Deployment

### Serverless Deployment (SCF)

1. **Install Tencent Cloud CLI**
   ```bash
   npm install -g @tencent/scf-cli
   ```

2. **Configure credentials**
   ```bash
   scf configure set --region ap-beijing --secret-id your-secret-id --secret-key your-secret-key
   ```

3. **Deploy to SCF**
   ```bash
   # Build for serverless
   pnpm build
   
   # Deploy function
   scf deploy --template-file template.yaml
   ```

### CVM Deployment (Fallback)

Similar to Alibaba Cloud ECS deployment:

1. **Create CVM Instance**
   - Instance Type: S5.LARGE8 or higher
   - Image: Ubuntu 20.04 LTS
   - Security Group: Allow HTTP, HTTPS, SSH

2. **Follow same Docker installation steps as Alibaba Cloud**

3. **Configure COS for file storage**
   ```bash
   # Environment variables for COS
   TENCENT_CLOUD_SECRET_ID=your_secret_id
   TENCENT_CLOUD_SECRET_KEY=your_secret_key
   TENCENT_CLOUD_REGION=ap-beijing
   ```

## Safety Features

- **Content Filtering**: Multi-layer filtering for age-appropriate content
- **Prompt Sanitization**: Automatic removal of inappropriate keywords
- **Rate Limiting**: Prevents abuse of AI APIs
- **User Monitoring**: All activities logged for review

## Monitoring and Logging

- **Application Logs**: Docker logs via `docker-compose logs`
- **Database Monitoring**: Prisma logging enabled
- **API Usage**: Track AI API usage and costs
- **Error Tracking**: Console error logging

## Development Scripts

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint
pnpm type-check       # Run TypeScript checks

# Database
pnpm db:migrate       # Run migrations
pnpm db:studio        # Open Prisma Studio
pnpm db:reset         # Reset database
pnpm db:seed          # Seed database (when implemented)

# Docker
pnpm docker:build     # Build Docker image
pnpm docker:up        # Start Docker services
pnpm docker:down      # Stop Docker services
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the troubleshooting guide

## Security

- Never commit API keys or secrets
- Use environment variables for sensitive data
- Regularly update dependencies
- Monitor for security vulnerabilities
