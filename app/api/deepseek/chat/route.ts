import { NextRequest, NextResponse } from 'next/server';
import { deepSeekClient } from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    const { messages, options } = await request.json();

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    const response = await deepSeekClient.chat(messages, {
      ...options,
      safeMode: true
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('DeepSeek API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}