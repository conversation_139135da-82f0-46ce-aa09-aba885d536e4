import { NextRequest, NextResponse } from 'next/server';
import { jimengClient } from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    const { prompt, ...options } = await request.json();

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    const response = await jimengClient.generateVideo({
      prompt,
      ...options
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Jimeng Video API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    const response = await jimengClient.checkVideoStatus(taskId);
    return NextResponse.json(response);
  } catch (error) {
    console.error('Jimeng Video Status API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}