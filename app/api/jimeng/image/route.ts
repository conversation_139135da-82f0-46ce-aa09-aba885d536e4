import { NextRequest, NextResponse } from 'next/server';
import { jimengClient } from '@/lib/ai';

export async function POST(request: NextRequest) {
  try {
    const { prompt, ...options } = await request.json();

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    const response = await jimengClient.generateImage({
      prompt,
      ...options
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Jimeng Image API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}