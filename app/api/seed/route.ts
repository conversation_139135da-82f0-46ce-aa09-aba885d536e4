import { NextResponse } from 'next/server';
import { hash } from 'bcryptjs';
import { prisma } from '@/lib/prisma';

export async function POST() {
  try {
    // Check if any users exist
    const existingUsers = await prisma.user.count();
    
    if (existingUsers > 0) {
      return NextResponse.json(
        { message: 'Users already exist, skipping seed' },
        { status: 200 }
      );
    }

    // Create test users
    const password = await hash('password123', 12);

    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'Test Student',
        age: 10,
        password,
        role: 'STUDENT' as const,
      },
      {
        email: '<EMAIL>',
        name: 'Test Teacher',
        age: 30,
        password,
        role: 'TEACHER' as const,
      },
      {
        email: '<EMAIL>',
        name: 'Test Parent',
        age: 35,
        password,
        role: 'PARENT' as const,
      }
    ];

    for (const userData of testUsers) {
      await prisma.user.create({
        data: userData
      });
    }

    return NextResponse.json(
      { 
        message: 'Test users created successfully',
        users: [
          { email: '<EMAIL>', password: 'password123', role: 'STUDENT' },
          { email: '<EMAIL>', password: 'password123', role: 'TEACHER' },
          { email: '<EMAIL>', password: 'password123', role: 'PARENT' }
        ]
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Seed error:', error);
    return NextResponse.json(
      { error: 'Failed to create test users' },
      { status: 500 }
    );
  }
}