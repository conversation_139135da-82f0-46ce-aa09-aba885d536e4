'use client';

import { useState } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Navbar from '@/components/Navbar';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError('Invalid email or password');
      } else {
        // Check session and redirect
        const session = await getSession();
        if (session) {
          router.push('/dashboard');
        }
      }
    } catch {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-sky">
      <Navbar />
      <div className="flex items-center justify-center min-h-screen">
      <div className="max-w-md w-full space-y-8 p-6">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-ai-yellow rounded-full flex items-center justify-center mb-4 shadow-button">
            <span className="text-2xl">🎨</span>
          </div>
          <h2 className="text-child-xl font-bold text-gray-900 mb-2">
            Welcome to AI Fun Lab!
          </h2>
          <p className="text-child-base text-gray-600">
            Create amazing images and videos with AI!
          </p>
        </div>
        
        {error && (
          <div className="alert-error animate-slide-up">
            <div className="flex items-center">
              <span className="text-lg mr-2">⚠️</span>
              <p className="text-child-sm font-medium">{error}</p>
            </div>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email-address" className="block text-child-sm font-medium text-gray-700 mb-2">
                📧 Email Address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                autoComplete="email"
                required
                className="input-field text-child-sm"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-child-sm font-medium text-gray-700 mb-2">
                🔒 Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                autoComplete="current-password"
                required
                className="input-field text-child-sm"
                placeholder="Enter your password"
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary w-full text-child-base font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="loading-spinner w-5 h-5 mr-3"></div>
              ) : (
                <span className="mr-2">🚀</span>
              )}
              {isLoading ? 'Signing in...' : 'Let&apos;s Go!'}
            </button>
          </div>
          
          <div className="text-center">
            <Link href="/register" className="btn-ghost text-child-sm">
              🌟 Don&apos;t have an account? Join us!
            </Link>
          </div>
        </form>
      </div>
      </div>
    </div>
  );
}