'use client';

import { useState } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Navbar from '@/components/Navbar';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError('邮箱或密码错误，请重试');
      } else {
        const session = await getSession();
        if (session) {
          router.push('/dashboard');
        }
      }
    } catch {
      setError('登录失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-sky">
      <Navbar />
      
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center animate-fade-in">
            <div className="mx-auto w-20 h-20 bg-gradient-fun rounded-full flex items-center justify-center mb-6 shadow-xl animate-float">
              <span className="text-3xl">🎨</span>
            </div>
            <h2 className="text-child-3xl font-bold text-gray-900 mb-2">
              欢迎回来！
            </h2>
            <p className="text-child-base text-gray-600">
              登录你的账户，继续创作之旅
            </p>
          </div>

          {/* Error Alert */}
          {error && (
            <div className="alert alert-error animate-slide-up">
              <div className="flex items-center">
                <span className="text-xl mr-3">⚠️</span>
                <p className="text-child-sm font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Login Form */}
          <div className="card p-8 animate-scale-in">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  📧 邮箱地址
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                  className="form-input text-child-sm"
                  placeholder="请输入你的邮箱"
                />
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  🔒 密码
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  autoComplete="current-password"
                  required
                  className="form-input text-child-sm"
                  placeholder="请输入你的密码"
                />
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-primary w-full btn-large font-semibold"
                >
                  {isLoading ? (
                    <div className="loading-spinner w-5 h-5 mr-3"></div>
                  ) : (
                    <span className="mr-2">🚀</span>
                  )}
                  {isLoading ? '登录中...' : '开始创作！'}
                </button>
              </div>

              <div className="text-center">
                <Link href="/register" className="btn btn-ghost">
                  🌟 还没有账户？立即注册
                </Link>
              </div>
            </form>
          </div>

          {/* Additional Info */}
          <div className="text-center animate-fade-in">
            <div className="card p-6 bg-gradient-fun">
              <h3 className="text-child-lg font-semibold text-gray-800 mb-2">
                🎯 快速体验
              </h3>
              <p className="text-child-sm text-gray-700">
                体验账户：<EMAIL><br />
                密码：demo123
              </p>
            </div>
          </div>

          {/* Safety Notice */}
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-green-50 border border-green-200 rounded-lg">
              <span className="text-green-600 mr-2">🛡️</span>
              <span className="text-sm text-green-800 font-medium">
                安全登录 • 家长放心
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}