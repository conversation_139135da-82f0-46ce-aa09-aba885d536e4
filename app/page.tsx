'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';

export default function Home() {
  const { data: session } = useSession();
  const [isLoaded, setIsLoaded] = useState(false);
  
  // Animation trigger on page load
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* 英雄区域 - 按照设计指南增强版 */}
      <section className="hero relative overflow-hidden">
        {/* 装饰性背景元素 */}
        <div className="absolute -top-10 -left-10 w-40 h-40 rounded-full bg-primary-yellow opacity-20 blur-3xl animate-float"></div>
        <div className="absolute top-1/4 -right-20 w-60 h-60 rounded-full bg-sky-blue opacity-20 blur-3xl animate-float-delayed"></div>
        <div className="absolute -bottom-10 left-1/3 w-40 h-40 rounded-full bg-energy-orange opacity-10 blur-3xl animate-float-slow"></div>
        
        <div className="container-wide relative z-10">
          <div className={`transition-all duration-1000 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {/* 主标题 - 增强版 */}
            <h1 className="hero-title mb-lg" aria-label="欢迎来到 AI Fun Lab">
              <span className="inline-block animate-bounce-gentle" style={{animationDelay: '0.1s'}}>🎨</span> 
              <span className="inline-block animate-fade-in" style={{animationDelay: '0.2s'}}>欢迎来到 </span>
              <span className="highlight bg-gradient-to-r from-primary-yellow to-energy-orange bg-clip-text text-transparent inline-block animate-pulse-gentle">
                AI Fun Lab
              </span>
            </h1>
            
            {/* 副标题 - 增强版 */}
            <p className="hero-subtitle mb-2xl max-w-3xl mx-auto" aria-label="为6-11岁小朋友设计的AI创作平台">
              为6-11岁小朋友设计的AI创作平台，安全有趣地探索人工智能的奇妙世界，创造属于你的数字艺术作品！
            </p>
            
            {/* 装饰性图标元素 */}
            <div className="absolute top-10 right-10 hidden lg:block">
              <div className="relative">
                <div className="w-16 h-16 rounded-full bg-soft-pink flex items-center justify-center shadow-md animate-float">
                  <span className="text-h3">🚀</span>
                </div>
                <div className="w-12 h-12 rounded-full bg-sky-blue absolute -bottom-6 -left-6 flex items-center justify-center shadow-md animate-float-delayed">
                  <span className="text-body">👽</span>
                </div>
              </div>
            </div>
            
            {/* CTA按钮组 - 增强版 */}
            <div className="flex flex-col sm:flex-row gap-lg justify-center">
              {session ? (
                <>
                  <Link 
                    href="/dashboard" 
                    className="btn btn-primary btn-large group relative overflow-hidden shadow-lg hover:shadow-xl transition-all"
                    aria-label="进入工作台"
                  >
                    <span className="relative z-10 flex items-center gap-sm">
                      <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🏠</span> 
                      <span>进入工作台</span>
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-primary-yellow to-energy-orange opacity-0 group-hover:opacity-20 transition-opacity"></span>
                  </Link>
                  <Link 
                    href="/create-image" 
                    className="btn btn-secondary btn-large group relative overflow-hidden shadow-md hover:shadow-lg transition-all"
                    aria-label="开始创作"
                  >
                    <span className="relative z-10 flex items-center gap-sm">
                      <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🎨</span> 
                      <span>开始创作</span>
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-sky-blue to-ocean-blue opacity-0 group-hover:opacity-10 transition-opacity"></span>
                  </Link>
                </>
              ) : (
                <>
                  <Link 
                    href="/register" 
                    className="btn btn-primary btn-large group relative overflow-hidden shadow-lg hover:shadow-xl transition-all"
                    aria-label="立即开始"
                  >
                    <span className="relative z-10 flex items-center gap-sm">
                      <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🌟</span> 
                      <span>立即开始</span>
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-primary-yellow to-energy-orange opacity-0 group-hover:opacity-20 transition-opacity"></span>
                  </Link>
                  <Link 
                    href="/login" 
                    className="btn btn-secondary btn-large group relative overflow-hidden shadow-md hover:shadow-lg transition-all"
                    aria-label="登录账户"
                  >
                    <span className="relative z-10 flex items-center gap-sm">
                      <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🔑</span> 
                      <span>登录账户</span>
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-sky-blue to-ocean-blue opacity-0 group-hover:opacity-10 transition-opacity"></span>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 核心功能展示 - 增强版 */}
      <section className="section bg-white relative overflow-hidden">
        {/* 装饰性背景元素 */}
        <div className="absolute top-20 right-0 w-40 h-40 rounded-full bg-sky-blue opacity-10 blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-20 left-10 w-32 h-32 rounded-full bg-primary-yellow opacity-10 blur-3xl animate-float"></div>
        
        <div className="container-wide relative z-10">
          <div className="text-center mb-2xl">
            <div className="inline-block mb-md">
              <span className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-primary-yellow to-energy-orange shadow-lg mb-md mx-auto animate-bounce-gentle">
                <span className="text-h2">🏁</span>
              </span>
            </div>
            <h2 className="text-h2 font-bold text-gray-900 mb-md relative inline-block">
              <span className="relative z-10">三大核心功能</span>
              <span className="absolute bottom-0 left-0 w-full h-3 bg-primary-yellow opacity-30 -z-10 transform -rotate-1"></span>
            </h2>
            <p className="text-body text-gray-600 max-w-2xl mx-auto">
              使用最先进的中国AI技术，为小朋友们提供安全、有趣、教育性的创作体验
            </p>
          </div>

          <div className="grid-cards">
            {/* AI图像生成卡片 */}
            <div className="card card-padding hover:shadow-lg transition-all duration-300 group" tabIndex={0}>
              <div className="text-center relative">
                {/* 图标容器 */}
                <div 
                  className="w-20 h-20 mx-auto mb-lg rounded-2xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all group-hover:scale-110 group-focus:scale-110 overflow-hidden"
                  style={{backgroundColor: 'var(--primary-yellow)'}}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-300 to-yellow-500 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity"></div>
                  <span className="text-h2 relative z-10 group-hover:scale-125 group-focus:scale-125 transition-transform">🎨</span>
                </div>
                
                {/* 标题 */}
                <h3 
                  className="text-h3 font-semibold mb-md transition-colors group-hover:text-ocean-blue group-focus:text-ocean-blue"
                  style={{color: 'var(--ocean-blue)'}}
                >
                  AI图像生成
                </h3>
                
                {/* 描述 */}
                <p className="text-body text-gray-600">
                  只需描述你的想法，我们的AI就能帮你创造出惊艳的图片。从可爱的小动物到梦幻的城堡，一切都能实现！
                </p>
                
                {/* 交互按钮 */}
                <Link 
                  href="/create-image"
                  className="btn btn-small btn-ghost mt-lg inline-flex items-center gap-xs group-hover:text-primary-yellow group-focus:text-primary-yellow transition-colors"
                  aria-label="开始创作图片"
                >
                  <span>开始创作</span>
                  <span className="transform group-hover:translate-x-1 group-focus:translate-x-1 transition-transform">→</span>
                </Link>
              </div>
            </div>

            {/* AI视频制作卡片 */}
            <div className="card card-padding hover:shadow-lg transition-all duration-300 group" tabIndex={0}>
              <div className="text-center relative">
                {/* 图标容器 */}
                <div 
                  className="w-20 h-20 mx-auto mb-lg rounded-2xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all group-hover:scale-110 group-focus:scale-110 overflow-hidden"
                  style={{backgroundColor: 'var(--sky-blue)'}}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-300 to-blue-500 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity"></div>
                  <span className="text-h2 relative z-10 group-hover:scale-125 group-focus:scale-125 transition-transform">🎬</span>
                </div>
                
                {/* 标题 */}
                <h3 
                  className="text-h3 font-semibold mb-md transition-colors group-hover:text-ocean-blue group-focus:text-ocean-blue"
                  style={{color: 'var(--ocean-blue)'}}
                >
                  AI视频制作
                </h3>
                
                {/* 描述 */}
                <p className="text-body text-gray-600">
                  将你的故事变成动态视频！输入文字描述，AI会为你制作出生动有趣的短视频内容。
                </p>
                
                {/* 交互按钮 */}
                <Link 
                  href="/create-video"
                  className="btn btn-small btn-ghost mt-lg inline-flex items-center gap-xs group-hover:text-sky-blue group-focus:text-sky-blue transition-colors"
                  aria-label="开始制作视频"
                >
                  <span>开始制作</span>
                  <span className="transform group-hover:translate-x-1 group-focus:translate-x-1 transition-transform">→</span>
                </Link>
              </div>
            </div>

            {/* AI智能助手卡片 */}
            <div className="card card-padding hover:shadow-lg transition-all duration-300 group" tabIndex={0}>
              <div className="text-center relative">
                {/* 图标容器 */}
                <div 
                  className="w-20 h-20 mx-auto mb-lg rounded-2xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all group-hover:scale-110 group-focus:scale-110 overflow-hidden"
                  style={{backgroundColor: 'var(--energy-orange)'}}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-300 to-orange-500 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity"></div>
                  <span className="text-h2 relative z-10 group-hover:scale-125 group-focus:scale-125 transition-transform">💬</span>
                </div>
                
                {/* 标题 */}
                <h3 
                  className="text-h3 font-semibold mb-md transition-colors group-hover:text-ocean-blue group-focus:text-ocean-blue"
                  style={{color: 'var(--ocean-blue)'}}
                >
                  AI智能助手
                </h3>
                
                {/* 描述 */}
                <p className="text-body text-gray-600">
                  与智能助手聊天，获取帮助、学习知识或只是聊聊天。我们的AI助手会用友好的方式回答你的问题。
                </p>
                
                {/* 交互按钮 */}
                <Link 
                  href="/chat"
                  className="btn btn-small btn-ghost mt-lg inline-flex items-center gap-xs group-hover:text-energy-orange group-focus:text-energy-orange transition-colors"
                  aria-label="开始聊天"
                >
                  <span>开始聊天</span>
                  <span className="transform group-hover:translate-x-1 group-focus:translate-x-1 transition-transform">→</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 平台统计 - 增强版 */}
      <section className="py-16 relative overflow-hidden" style={{backgroundColor: 'var(--soft-pink)'}}>
        {/* 装饰性背景元素 */}
        <div className="absolute top-0 left-1/4 w-64 h-64 rounded-full bg-primary-yellow opacity-5 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-64 h-64 rounded-full bg-sky-blue opacity-5 blur-3xl"></div>
        
        <div className="container-wide relative z-10">
          <div className="text-center mb-xl">
            <div className="inline-block mb-md">
              <span className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-sky-blue to-ocean-blue shadow-lg mb-md mx-auto animate-pulse-gentle">
                <span className="text-h2">🏆</span>
              </span>
            </div>
            <h2 className="text-h2 font-bold text-gray-900 mb-md relative inline-block">
              <span className="relative z-10">平台成果</span>
              <span className="absolute bottom-0 left-0 w-full h-3 bg-sky-blue opacity-30 -z-10 transform -rotate-1"></span>
            </h2>
            <p className="text-body text-gray-700 max-w-2xl mx-auto">
              已经有很多小朋友在这里创作出了精彩的作品
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-lg">
            {[
              { icon: '👥', value: '1,000+', label: '注册用户', color: 'var(--primary-yellow)', animation: 'animate-bounce-gentle' },
              { icon: '🎨', value: '10,000+', label: '创作图片', color: 'var(--sky-blue)', animation: 'animate-float' },
              { icon: '🎬', value: '2,000+', label: '生成视频', color: 'var(--energy-orange)', animation: 'animate-bounce-gentle' },
              { icon: '⭐', value: '98%', label: '满意度', color: 'var(--success-green)', animation: 'animate-pulse-gentle' }
            ].map((stat, index) => (
              <div 
                key={index} 
                className="card card-padding-mobile text-center transform hover:scale-105 transition-transform duration-300 hover:shadow-md"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div 
                  className={`w-16 h-16 mx-auto mb-md rounded-2xl flex items-center justify-center text-h2 shadow-md ${stat.animation}`}
                  style={{
                    background: `linear-gradient(135deg, ${stat.color}, ${stat.color}88)`,
                    animationDelay: `${index * 0.2}s`
                  }}
                >
                  {stat.icon}
                </div>
                <div className="text-h3 font-bold text-gray-900 mb-xs">{stat.value}</div>
                <div className="text-body-small text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 行动召唤区域 - 增强版 */}
      <section className="py-20 bg-white relative overflow-hidden">
        {/* 装饰性背景元素 */}
        <div className="absolute top-0 right-0 w-40 h-40 rounded-full bg-primary-yellow opacity-10 blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 rounded-full bg-energy-orange opacity-10 blur-3xl animate-float"></div>
        
        <div className="container-narrow text-center relative z-10">
          <div 
            className="card card-padding rounded-3xl shadow-xl overflow-hidden relative" 
            style={{
              background: 'linear-gradient(135deg, var(--ocean-blue), var(--sky-blue))'
            }}
          >
            {/* 装饰性元素 */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-5 rounded-full transform translate-x-1/2 -translate-y-1/2"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-white opacity-5 rounded-full transform -translate-x-1/2 translate-y-1/2"></div>
            
            <div className="relative z-10">
              <div className="inline-block mb-lg">
                <span className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-white bg-opacity-20 shadow-lg mb-md mx-auto animate-float">
                  <span className="text-h1 text-white">🚀</span>
                </span>
              </div>
              
              <h2 className="text-h2 font-bold text-white mb-lg">
                <span className="bg-white bg-opacity-10 px-4 py-2 rounded-lg inline-block">准备好开始你的AI创作之旅了吗？</span>
              </h2>
              
              <p className="text-body-large text-white mb-2xl opacity-90 max-w-2xl mx-auto">
                加入我们，探索无限的创意可能性，让想象力插上AI的翼膀！
              </p>
              
              {!session && (
                <div className="flex flex-col sm:flex-row gap-lg justify-center">
                  <Link 
                    href="/register" 
                    className="btn btn-primary btn-large group relative overflow-hidden shadow-lg hover:shadow-xl transition-all"
                    aria-label="免费注册"
                  >
                    <span className="relative z-10 flex items-center gap-sm">
                      <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🌟</span> 
                      <span>免费注册</span>
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-primary-yellow to-energy-orange opacity-0 group-hover:opacity-20 transition-opacity"></span>
                  </Link>
                  
                  <Link 
                    href="/login" 
                    className="btn btn-ghost btn-large text-white border-2 border-white group relative overflow-hidden hover:bg-white hover:text-ocean-blue transition-all"
                    aria-label="立即登录"
                  >
                    <span className="relative z-10 flex items-center gap-sm">
                      <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🔑</span> 
                      <span>立即登录</span>
                    </span>
                  </Link>
                </div>
              )}
              
              {session && (
                <Link 
                  href="/dashboard" 
                  className="btn btn-primary btn-large group relative overflow-hidden shadow-lg hover:shadow-xl transition-all"
                  aria-label="回到工作台"
                >
                  <span className="relative z-10 flex items-center gap-sm">
                    <span className="inline-block transition-transform group-hover:scale-125 group-hover:rotate-12">🏠</span> 
                    <span>回到工作台</span>
                  </span>
                  <span className="absolute inset-0 bg-gradient-to-r from-primary-yellow to-energy-orange opacity-0 group-hover:opacity-20 transition-opacity"></span>
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 增强版页脚 */}
      <footer className="bg-gray-900 text-white py-12 relative overflow-hidden">
        {/* 装饰性背景元素 */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-yellow via-sky-blue to-energy-orange"></div>
        <div className="absolute bottom-0 right-0 w-40 h-40 rounded-full bg-ocean-blue opacity-10 blur-3xl"></div>
        <div className="absolute top-20 left-20 w-32 h-32 rounded-full bg-energy-orange opacity-5 blur-3xl"></div>
        
        <div className="container-wide relative z-10">
          <div className="flex flex-col items-center justify-center mb-lg">
            <div className="mb-md">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-primary-yellow to-energy-orange p-1 shadow-lg">
                <div className="w-full h-full rounded-full bg-gray-900 flex items-center justify-center">
                  <span className="text-h3 animate-pulse-gentle">🎨</span>
                </div>
              </div>
            </div>
            <h3 className="text-h3 font-bold mb-xs text-white bg-gradient-to-r from-primary-yellow to-energy-orange bg-clip-text text-transparent">
              AI Fun Lab
            </h3>
            <p className="text-body text-gray-300 mb-lg max-w-lg mx-auto text-center">
              专为儿童设计的AI创作平台 • 安全 • 有趣 • 教育
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-lg mb-xl">
            <div className="text-center">
              <h4 className="text-body-large font-semibold mb-md text-white">创作工具</h4>
              <ul className="space-y-sm">
                <li>
                  <Link 
                    href="/create-image" 
                    className="text-gray-400 hover:text-primary-yellow transition-colors flex items-center justify-center gap-xs"
                    aria-label="图像生成"
                  >
                    <span>🎨</span>
                    <span>图像生成</span>
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/create-video" 
                    className="text-gray-400 hover:text-sky-blue transition-colors flex items-center justify-center gap-xs"
                    aria-label="视频制作"
                  >
                    <span>🎬</span>
                    <span>视频制作</span>
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/chat" 
                    className="text-gray-400 hover:text-energy-orange transition-colors flex items-center justify-center gap-xs"
                    aria-label="智能助手"
                  >
                    <span>💬</span>
                    <span>智能助手</span>
                  </Link>
                </li>
              </ul>
            </div>
            
            <div className="text-center">
              <h4 className="text-body-large font-semibold mb-md text-white">帮助中心</h4>
              <ul className="space-y-sm">
                <li>
                  <Link 
                    href="/help" 
                    className="text-gray-400 hover:text-primary-yellow transition-colors flex items-center justify-center gap-xs"
                    aria-label="使用指南"
                  >
                    <span>📖</span>
                    <span>使用指南</span>
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/faq" 
                    className="text-gray-400 hover:text-sky-blue transition-colors flex items-center justify-center gap-xs"
                    aria-label="常见问题"
                  >
                    <span>❓</span>
                    <span>常见问题</span>
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/contact" 
                    className="text-gray-400 hover:text-energy-orange transition-colors flex items-center justify-center gap-xs"
                    aria-label="联系我们"
                  >
                    <span>✉️</span>
                    <span>联系我们</span>
                  </Link>
                </li>
              </ul>
            </div>
            
            <div className="text-center">
              <h4 className="text-body-large font-semibold mb-md text-white">法律条款</h4>
              <ul className="space-y-sm">
                <li>
                  <Link 
                    href="/terms" 
                    className="text-gray-400 hover:text-primary-yellow transition-colors flex items-center justify-center gap-xs"
                    aria-label="使用条款"
                  >
                    <span>📃</span>
                    <span>使用条款</span>
                  </Link>
                </li>
                <li>
                  <Link 
                    href="/privacy" 
                    className="text-gray-400 hover:text-sky-blue transition-colors flex items-center justify-center gap-xs"
                    aria-label="隐私政策"
                  >
                    <span>🔒</span>
                    <span>隐私政策</span>
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-lg">
            <div className="flex flex-col md:flex-row justify-between items-center gap-md">
              <div className="text-small text-gray-500">
                © 2025 AI Fun Lab. 保留所有权利。
              </div>
              <div className="flex items-center gap-md">
                <a 
                  href="#" 
                  className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-primary-yellow hover:text-white transition-all"
                  aria-label="微信"
                >
                  <span>📱</span>
                </a>
                <a 
                  href="#" 
                  className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-sky-blue hover:text-white transition-all"
                  aria-label="微博"
                >
                  <span>🌐</span>
                </a>
                <a 
                  href="#" 
                  className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-energy-orange hover:text-white transition-all"
                  aria-label="知乎"
                >
                  <span>❓</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}