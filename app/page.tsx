'use client';

import Link from 'next/link';
import { useSession } from 'next-auth/react';
import Navbar from '@/components/Navbar';

export default function Home() {
  const { data: session } = useSession();

  return (
    <div className="min-h-screen">
      <Navbar />
      
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <div className="animate-fade-in">
            <h1 className="hero-title text-balance">
              欢迎来到 <span className="text-gradient-fun">AI Fun Lab</span>
            </h1>
            <p className="hero-subtitle">
              为6-11岁小朋友设计的AI创作平台，安全有趣地探索人工智能的奇妙世界，创造属于你的数字艺术作品！
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
              {session ? (
                <>
                  <Link href="/dashboard" className="btn btn-fun btn-large">
                    🏠 进入工作台
                  </Link>
                  <Link href="/create-image" className="btn btn-secondary btn-large">
                    🎨 开始创作
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/register" className="btn btn-fun btn-large">
                    🌟 立即开始
                  </Link>
                  <Link href="/login" className="btn btn-secondary btn-large">
                    🔑 登录账户
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
        
        {/* Floating Elements */}
        <div className="absolute top-20 left-10 animate-float">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl">
            🎨
          </div>
        </div>
        <div className="absolute top-40 right-20 animate-float" style={{animationDelay: '2s'}}>
          <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl">
            ✨
          </div>
        </div>
        <div className="absolute bottom-20 left-1/4 animate-float" style={{animationDelay: '4s'}}>
          <div className="w-14 h-14 bg-white/20 rounded-full flex items-center justify-center text-2xl">
            🚀
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="section bg-white">
        <div className="container-wide">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-child-3xl font-bold text-gray-900 mb-4">
              🎯 三大核心功能
            </h2>
            <p className="text-child-lg text-gray-600 max-w-2xl mx-auto">
              使用最先进的中国AI技术，为小朋友们提供安全、有趣、教育性的创作体验
            </p>
          </div>

          <div className="grid grid-auto-fit gap-8">
            <div className="feature-card animate-scale-in">
              <div className="feature-icon">
                <span className="text-3xl">🎨</span>
              </div>
              <h3 className="feature-title">AI图像生成</h3>
              <p className="feature-description">
                只需描述你的想法，我们的AI就能帮你创造出惊艳的图片。从可爱的小动物到梦幻的城堡，一切都能实现！
              </p>
            </div>

            <div className="feature-card animate-scale-in" style={{animationDelay: '0.2s'}}>
              <div className="feature-icon">
                <span className="text-3xl">🎬</span>
              </div>
              <h3 className="feature-title">AI视频制作</h3>
              <p className="feature-description">
                将你的故事变成动态视频！输入文字描述，AI会为你制作出生动有趣的短视频内容。
              </p>
            </div>

            <div className="feature-card animate-scale-in" style={{animationDelay: '0.4s'}}>
              <div className="feature-icon">
                <span className="text-3xl">🤖</span>
              </div>
              <h3 className="feature-title">智能助手</h3>
              <p className="feature-description">
                有疑问？想学习？我们的AI助手随时为你答疑解惑，是你最好的学习伙伴！
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section bg-gradient-sky">
        <div className="container-wide">
          <div className="text-center mb-16">
            <h2 className="text-child-3xl font-bold text-gray-900 mb-4">
              🏆 平台数据
            </h2>
            <p className="text-child-lg text-gray-700">
              已经有很多小朋友在这里创作出了精彩的作品
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="metric-card text-center card-hover">
              <div className="metric-icon bg-gradient-fun text-white mx-auto">
                👥
              </div>
              <div className="metric-value">1,000+</div>
              <div className="metric-label">注册用户</div>
            </div>

            <div className="metric-card text-center card-hover">
              <div className="metric-icon bg-gradient-primary text-white mx-auto">
                🎨
              </div>
              <div className="metric-value">10,000+</div>
              <div className="metric-label">创作图片</div>
            </div>

            <div className="metric-card text-center card-hover">
              <div className="metric-icon bg-gradient-sunset text-white mx-auto">
                🎬
              </div>
              <div className="metric-value">2,000+</div>
              <div className="metric-label">生成视频</div>
            </div>

            <div className="metric-card text-center card-hover">
              <div className="metric-icon bg-indigo-500 text-white mx-auto">
                ⭐
              </div>
              <div className="metric-value">98%</div>
              <div className="metric-label">满意度</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section bg-white">
        <div className="container-narrow text-center">
          <div className="card card-gradient p-12 animate-scale-in">
            <h2 className="text-child-3xl font-bold mb-6">
              🚀 准备好开始你的AI创作之旅了吗？
            </h2>
            <p className="text-child-lg mb-8 text-white/90">
              加入我们，探索无限的创意可能性，让想象力插上AI的翅膀！
            </p>
            
            {!session && (
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/register" className="btn btn-fun btn-large">
                  🌟 免费注册
                </Link>
                <Link href="/login" className="btn btn-secondary btn-large">
                  🔑 立即登录
                </Link>
              </div>
            )}
            
            {session && (
              <Link href="/dashboard" className="btn btn-fun btn-large">
                🏠 回到工作台
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container-wide">
          <div className="text-center">
            <h3 className="text-child-xl font-bold mb-4">AI Fun Lab</h3>
            <p className="text-gray-300 mb-6">
              专为儿童设计的AI创作平台 • 安全 • 有趣 • 教育
            </p>
            <div className="flex justify-center space-x-6 text-sm text-gray-400">
              <span>© 2025 AI Fun Lab</span>
              <span>•</span>
              <span>隐私政策</span>
              <span>•</span>
              <span>使用条款</span>
              <span>•</span>
              <span>联系我们</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}