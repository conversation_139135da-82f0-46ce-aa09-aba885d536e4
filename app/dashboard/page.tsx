'use client';

import Link from 'next/link';
import { useRequireAuth } from '@/lib/hooks/useAuth';
import Navbar from '@/components/Navbar';

export default function DashboardPage() {
  const { user, isLoading } = useRequireAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-16 h-16"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }
  return (
    <div className="min-h-screen bg-gradient-sky">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          <div className="md:flex md:items-center md:justify-between">
            <div className="flex-1 min-w-0">
              <h2 className="text-child-xl font-bold text-gray-900 animate-fade-in">
                Welcome back, {user.name || user.email}! 🎉
              </h2>
            </div>
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <Link
                href="/create-image"
                className="btn-primary animate-bounce-gentle"
              >
                <span className="mr-2">🎨</span>
                Create Image
              </Link>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div className="card hover:animate-bounce-gentle">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-ai-yellow rounded-full flex items-center justify-center shadow-button">
                  <span className="text-2xl">🎨</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-child-sm font-medium text-gray-600">
                    Images Created
                  </dt>
                  <dd className="text-child-lg font-bold text-gray-900">0</dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card hover:animate-bounce-gentle">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-ai-blue rounded-full flex items-center justify-center shadow-button">
                  <span className="text-2xl">🎞️</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-child-sm font-medium text-gray-600">
                    Videos Created
                  </dt>
                  <dd className="text-child-lg font-bold text-gray-900">0</dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card hover:animate-bounce-gentle">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-ai-orange rounded-full flex items-center justify-center shadow-button">
                  <span className="text-2xl">💬</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-child-sm font-medium text-gray-600">
                    Conversations
                  </dt>
                  <dd className="text-child-lg font-bold text-gray-900">0</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <div className="card">
            <div className="text-center">
              <h3 className="text-child-lg font-bold text-gray-900 mb-4">
                🌆 Recent Creations
              </h3>
              <div className="mb-6">
                <p className="text-child-base text-gray-600">Your recent AI-generated images and videos will appear here.</p>
              </div>
              <div className="py-12">
                <div className="text-6xl mb-4 animate-pulse-soft">🎨</div>
                <p className="text-child-base text-gray-500 mb-6">No creations yet. Start creating!</p>
                <Link
                  href="/create-image"
                  className="btn-secondary animate-bounce-gentle"
                >
                  <span className="mr-2">✨</span>
                  Create Your First Image
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}