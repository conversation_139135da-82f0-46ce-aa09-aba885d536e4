'use client';

import Link from 'next/link';
import { useRequireAuth } from '@/lib/hooks/useAuth';
import Navbar from '@/components/Navbar';

export default function DashboardPage() {
  const { user, isLoading } = useRequireAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex items-center justify-center h-64">
          <div className="loading-spinner w-16 h-16"></div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      {/* Welcome Hero */}
      <section className="bg-gradient-primary py-16">
        <div className="container-wide">
          <div className="text-center text-white animate-fade-in">
            <div className="mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-6 animate-float">
              <span className="text-3xl">🎨</span>
            </div>
            <h1 className="text-child-3xl font-bold mb-4">
              欢迎回来，{user.name || user.email}！ 🎉
            </h1>
            <p className="text-child-lg text-white/90 mb-8">
              准备好创造更多精彩的作品了吗？让我们开始新的创作之旅！
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create-image" className="btn btn-fun btn-large">
                🎨 创作图片
              </Link>
              <Link href="/create-video" className="btn btn-secondary btn-large">
                🎬 制作视频
              </Link>
              <Link href="/chat" className="btn btn-ghost btn-large text-white border-white hover:bg-white hover:text-gray-900">
                💬 AI助手
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Overview */}
      <section className="section-sm">
        <div className="container-wide">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 animate-scale-in">
            <div className="metric-card card-hover">
              <div className="metric-icon bg-gradient-fun text-white">
                🎨
              </div>
              <div className="metric-value">0</div>
              <div className="metric-label">创作图片</div>
              <div className="mt-3">
                <Link href="/create-image" className="btn btn-small btn-primary">
                  开始创作
                </Link>
              </div>
            </div>

            <div className="metric-card card-hover">
              <div className="metric-icon bg-gradient-primary text-white">
                🎬
              </div>
              <div className="metric-value">0</div>
              <div className="metric-label">制作视频</div>
              <div className="mt-3">
                <Link href="/create-video" className="btn btn-small btn-primary">
                  制作视频
                </Link>
              </div>
            </div>

            <div className="metric-card card-hover">
              <div className="metric-icon bg-gradient-sunset text-white">
                💬
              </div>
              <div className="metric-value">0</div>
              <div className="metric-label">AI对话</div>
              <div className="mt-3">
                <Link href="/chat" className="btn btn-small btn-primary">
                  开始聊天
                </Link>
              </div>
            </div>

            <div className="metric-card card-hover">
              <div className="metric-icon bg-indigo-500 text-white">
                ⭐
              </div>
              <div className="metric-value">新手</div>
              <div className="metric-label">创作等级</div>
              <div className="mt-3">
                <span className="inline-block px-3 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                  加油创作！
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="section-sm bg-white">
        <div className="container-wide">
          <div className="text-center mb-12">
            <h2 className="text-child-2xl font-bold text-gray-900 mb-4">
              🚀 快速开始
            </h2>
            <p className="text-child-base text-gray-600">
              选择你想要的创作方式，开始你的AI之旅
            </p>
          </div>

          <div className="grid grid-auto-fit gap-8">
            <div className="feature-card card-interactive animate-scale-in">
              <div className="feature-icon">
                <span className="text-3xl">🎨</span>
              </div>
              <h3 className="feature-title">图片生成</h3>
              <p className="feature-description mb-6">
                输入你的想法，AI会为你创造出美丽的图片
              </p>
              <Link href="/create-image" className="btn btn-primary w-full">
                立即开始
              </Link>
            </div>

            <div className="feature-card card-interactive animate-scale-in" style={{animationDelay: '0.2s'}}>
              <div className="feature-icon">
                <span className="text-3xl">🎬</span>
              </div>
              <h3 className="feature-title">视频制作</h3>
              <p className="feature-description mb-6">
                描述你的故事，AI会制作出生动的视频
              </p>
              <Link href="/create-video" className="btn btn-primary w-full">
                制作视频
              </Link>
            </div>

            <div className="feature-card card-interactive animate-scale-in" style={{animationDelay: '0.4s'}}>
              <div className="feature-icon">
                <span className="text-3xl">🤖</span>
              </div>
              <h3 className="feature-title">AI助手</h3>
              <p className="feature-description mb-6">
                有问题？想学习？AI助手随时为你解答
              </p>
              <Link href="/chat" className="btn btn-primary w-full">
                开始对话
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Creations */}
      <section className="section-sm">
        <div className="container-wide">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-child-2xl font-bold text-gray-900">
              📚 我的作品
            </h2>
            <Link href="/gallery" className="btn btn-ghost">
              查看全部
            </Link>
          </div>

          <div className="card p-12 text-center">
            <div className="mx-auto w-24 h-24 bg-gradient-sky rounded-full flex items-center justify-center mb-6 animate-float">
              <span className="text-4xl">🎨</span>
            </div>
            <h3 className="text-child-xl font-semibold text-gray-900 mb-4">
              还没有作品哦！
            </h3>
            <p className="text-child-base text-gray-600 mb-8 max-w-md mx-auto">
              开始你的第一个创作吧！无论是图片、视频还是和AI聊天，都会是很棒的体验。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/create-image" className="btn btn-fun btn-large">
                🎨 创作第一张图片
              </Link>
              <Link href="/create-video" className="btn btn-secondary btn-large">
                🎬 制作第一个视频
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Learning Section */}
      <section className="section-sm bg-gradient-sky">
        <div className="container-wide">
          <div className="text-center mb-12">
            <h2 className="text-child-2xl font-bold text-gray-900 mb-4">
              📖 学习与探索
            </h2>
            <p className="text-child-base text-gray-700">
              了解AI技术，提升你的创作技能
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="card card-hover p-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-xl">📚</span>
              </div>
              <h3 className="text-child-lg font-semibold text-gray-900 mb-2">
                AI基础知识
              </h3>
              <p className="text-child-sm text-gray-600 mb-4">
                了解人工智能的基本概念和工作原理
              </p>
              <Link href="/learn/basics" className="btn btn-small btn-secondary">
                开始学习
              </Link>
            </div>

            <div className="card card-hover p-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-xl">🎯</span>
              </div>
              <h3 className="text-child-lg font-semibold text-gray-900 mb-2">
                创作技巧
              </h3>
              <p className="text-child-sm text-gray-600 mb-4">
                学习如何写出更好的描述来获得理想作品
              </p>
              <Link href="/learn/tips" className="btn btn-small btn-secondary">
                查看技巧
              </Link>
            </div>

            <div className="card card-hover p-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <span className="text-xl">🏆</span>
              </div>
              <h3 className="text-child-lg font-semibold text-gray-900 mb-2">
                作品展示
              </h3>
              <p className="text-child-sm text-gray-600 mb-4">
                看看其他小朋友的精彩创作，获得灵感
              </p>
              <Link href="/showcase" className="btn btn-small btn-secondary">
                浏览作品
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}