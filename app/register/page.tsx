'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Navbar from '@/components/Navbar';

export default function RegisterPage() {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [age, setAge] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Validation
    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      setIsLoading(false);
      return;
    }

    if (parseInt(age) < 6 || parseInt(age) > 18) {
      setError('年龄需要在6-18岁之间');
      setIsLoading(false);
      return;
    }

    if (password.length < 6) {
      setError('密码至少需要6位字符');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name,
          password,
          age: parseInt(age),
        }),
      });

      if (response.ok) {
        setSuccess(true);
        setTimeout(() => {
          router.push('/login?message=注册成功，请登录');
        }, 2000);
      } else {
        const data = await response.json();
        setError(data.error || '注册失败，请重试');
      }
    } catch {
      setError('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-sky">
        <Navbar />
        <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full text-center">
            <div className="card p-8 animate-scale-in">
              <div className="mx-auto w-20 h-20 bg-gradient-fun rounded-full flex items-center justify-center mb-6 shadow-xl animate-float">
                <span className="text-3xl">🎉</span>
              </div>
              <h2 className="text-child-2xl font-bold text-gray-900 mb-4">
                注册成功！
              </h2>
              <p className="text-child-base text-gray-600 mb-6">
                欢迎加入AI Fun Lab大家庭！<br />
                正在跳转到登录页面...
              </p>
              <div className="loading-spinner w-8 h-8 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-sky">
      <Navbar />
      
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center animate-fade-in">
            <div className="mx-auto w-20 h-20 bg-gradient-fun rounded-full flex items-center justify-center mb-6 shadow-xl animate-float">
              <span className="text-3xl">🌟</span>
            </div>
            <h2 className="text-child-3xl font-bold text-gray-900 mb-2">
              加入AI Fun Lab！
            </h2>
            <p className="text-child-base text-gray-600">
              创建你的账户，开始AI创作之旅
            </p>
          </div>

          {/* Error Alert */}
          {error && (
            <div className="alert alert-error animate-slide-up">
              <div className="flex items-center">
                <span className="text-xl mr-3">⚠️</span>
                <p className="text-child-sm font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Registration Form */}
          <div className="card p-8 animate-scale-in">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name" className="form-label">
                  👤 你的姓名
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  className="form-input text-child-sm"
                  placeholder="请输入你的真实姓名"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="email" className="form-label">
                  📧 邮箱地址
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  autoComplete="email"
                  required
                  className="form-input text-child-sm"
                  placeholder="请输入邮箱地址"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="age" className="form-label">
                  🎂 你的年龄
                </label>
                <input
                  id="age"
                  name="age"
                  type="number"
                  value={age}
                  onChange={(e) => setAge(e.target.value)}
                  min="6"
                  max="18"
                  required
                  className="form-input text-child-sm"
                  placeholder="请输入你的年龄 (6-18岁)"
                />
                <p className="text-xs text-gray-500 mt-1">
                  我们为不同年龄段提供适合的内容
                </p>
              </div>
              
              <div className="form-group">
                <label htmlFor="password" className="form-label">
                  🔒 设置密码
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="form-input text-child-sm"
                  placeholder="请设置至少6位密码"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="confirm-password" className="form-label">
                  🔍 确认密码
                </label>
                <input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="form-input text-child-sm"
                  placeholder="请再次输入密码"
                />
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-fun w-full btn-large font-semibold"
                >
                  {isLoading ? (
                    <div className="loading-spinner w-5 h-5 mr-3"></div>
                  ) : (
                    <span className="mr-2">🎉</span>
                  )}
                  {isLoading ? '注册中...' : '开始我的创作之旅！'}
                </button>
              </div>

              <div className="text-center">
                <Link href="/login" className="btn btn-ghost">
                  🔑 已有账户？立即登录
                </Link>
              </div>
            </form>
          </div>

          {/* Safety Features */}
          <div className="space-y-4 animate-fade-in">
            <div className="card p-6 bg-green-50 border-green-200">
              <h3 className="text-child-lg font-semibold text-green-800 mb-3 flex items-center">
                <span className="mr-2">🛡️</span>
                安全保障
              </h3>
              <div className="space-y-2 text-child-sm text-green-700">
                <p>✅ 内容安全过滤，家长放心</p>
                <p>✅ 年龄适应性设计，学习更有效</p>
                <p>✅ 隐私信息保护，数据更安全</p>
              </div>
            </div>

            <div className="card p-6 bg-blue-50 border-blue-200">
              <h3 className="text-child-lg font-semibold text-blue-800 mb-3 flex items-center">
                <span className="mr-2">🎯</span>
                学习收获
              </h3>
              <div className="space-y-2 text-child-sm text-blue-700">
                <p>🎨 培养创意思维和艺术审美</p>
                <p>🧠 学习AI技术基础知识</p>
                <p>🤝 提升数字素养和创作能力</p>
              </div>
            </div>
          </div>

          {/* Terms */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              注册即表示同意我们的
              <Link href="/terms" className="text-indigo-600 hover:text-indigo-500">
                使用条款
              </Link>
              和
              <Link href="/privacy" className="text-indigo-600 hover:text-indigo-500">
                隐私政策
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}