'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Navbar from '@/components/Navbar';

export default function RegisterPage() {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [age, setAge] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic validation
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    if (parseInt(age) < 8 || parseInt(age) > 18) {
      setError('Age must be between 8 and 18 for students');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          name,
          password,
          age: parseInt(age),
        }),
      });

      if (response.ok) {
        router.push('/login?message=Registration successful');
      } else {
        const data = await response.json();
        setError(data.error || 'Registration failed');
      }
    } catch {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-sky">
      <Navbar />
      <div className="flex items-center justify-center min-h-screen">
      <div className="max-w-md w-full space-y-8 p-6">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-ai-orange rounded-full flex items-center justify-center mb-4 shadow-button">
            <span className="text-2xl">🌟</span>
          </div>
          <h2 className="text-child-xl font-bold text-gray-900 mb-2">
            Join AI Fun Lab!
          </h2>
          <p className="text-child-base text-gray-600">
            Create your account to start making amazing content!
          </p>
        </div>
        
        {error && (
          <div className="alert-error animate-slide-up">
            <div className="flex items-center">
              <span className="text-lg mr-2">⚠️</span>
              <p className="text-child-sm font-medium">{error}</p>
            </div>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-child-sm font-medium text-gray-700 mb-2">
                👤 Full Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="input-field text-child-sm"
                placeholder="Your awesome name"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-child-sm font-medium text-gray-700 mb-2">
                📧 Email Address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                autoComplete="email"
                required
                className="input-field text-child-sm"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label htmlFor="age" className="block text-child-sm font-medium text-gray-700 mb-2">
                🎂 Age
              </label>
              <input
                id="age"
                name="age"
                type="number"
                value={age}
                onChange={(e) => setAge(e.target.value)}
                min="8"
                max="18"
                required
                className="input-field text-child-sm"
                placeholder="How old are you? (8-18)"
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block text-child-sm font-medium text-gray-700 mb-2">
                🔒 Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="input-field text-child-sm"
                placeholder="Create a strong password"
              />
            </div>
            
            <div>
              <label htmlFor="confirm-password" className="block text-child-sm font-medium text-gray-700 mb-2">
                🔍 Confirm Password
              </label>
              <input
                id="confirm-password"
                name="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className="input-field text-child-sm"
                placeholder="Type your password again"
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary w-full text-child-base font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="loading-spinner w-5 h-5 mr-3"></div>
              ) : (
                <span className="mr-2">🎉</span>
              )}
              {isLoading ? 'Creating account...' : 'Join the Fun!'}
            </button>
          </div>
          
          <div className="text-center">
            <Link href="/login" className="btn-ghost text-child-sm">
              🔑 Already have an account? Sign in
            </Link>
          </div>
        </form>
      </div>
      </div>
    </div>
  );
}