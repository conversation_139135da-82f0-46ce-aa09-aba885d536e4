@import "tailwindcss";

@layer base {
  /* 导入设计指南指定的字体 */
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap');

  /* 设计指南色彩系统 */
  :root {
    /* 主色板 - 温暖友好，高对比度 */
    --primary-yellow: #FFB703;    /* 阳光黄 - 主要CTA按钮 */
    --ocean-blue: #219EBC;        /* 海洋蓝 - 信息和导航 */
    --sky-blue: #8ECAE6;          /* 天空蓝 - 次要元素 */
    --energy-orange: #FB8500;     /* 活力橙 - 强调和警告 */
    --soft-pink: #FFDDD2;         /* 柔和粉 - 背景和卡片 */
    
    /* 功能色板 */
    --success-green: #06D6A0;     /* 成功绿 */
    --error-red: #F72585;         /* 错误红 */
    --warning-amber: #F77F00;     /* 警告琥珀 */
    --info-blue: #0077B6;         /* 信息蓝 */
    --neutral-gray: #6C757D;      /* 中性灰 */
    
    /* 基础网格: 8px */
    --space-xs: 4px;   /* 紧密元素 */
    --space-sm: 8px;   /* 相关元素 */
    --space-md: 12px;  /* 组件内间距 */
    --space-lg: 16px;  /* 组件间间距 */
    --space-xl: 24px;  /* 区块间距 */
    --space-2xl: 32px; /* 节点间距 */
    --space-3xl: 48px; /* 页面区域间距 */
    --space-4xl: 64px; /* 大型布局间距 */
    
    /* 圆角系统 */
    --radius-xs: 4px;   /* 小标签 */
    --radius-sm: 6px;   /* 按钮、输入框 */
    --radius-md: 8px;   /* 卡片 */
    --radius-lg: 16px;  /* 主要容器 - 推荐默认 */
    --radius-xl: 24px;  /* 模态框 */
    --radius-2xl: 32px; /* 大型面板 */
    
    /* 阴影系统 */
    --shadow-card: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 16px rgba(0,0,0,0.15);
    --shadow-modal: 0 8px 32px rgba(0,0,0,0.2);
    --shadow-inner: inset 0 2px 4px rgba(0,0,0,0.1);
  }

  /* 基础样式 */
  html {
    font-family: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-white text-gray-900 min-h-screen;
    font-size: 16px; /* 最小字号 */
    line-height: 1.5; /* 设计指南规定的行高 */
  }

  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
    line-height: 1.2; /* 标题行高 */
    font-weight: 600;
    margin: 0;
  }

  /* 移动端字号体系 (1.25倍等比缩放) */
  .text-display { font-size: 32px; } /* 页面标题 */
  .text-h1 { font-size: 28px; }      /* 主标题 */
  .text-h2 { font-size: 24px; }      /* 副标题 */
  .text-h3 { font-size: 20px; }      /* 章节标题 */
  .text-body-large { font-size: 18px; } /* 重要正文 */
  .text-body { font-size: 16px; }    /* 标准正文 - 最小字号 */
  .text-small { font-size: 14px; }   /* 辅助文字，限制使用 */

  /* 桌面端字号体系 */
  @media (min-width: 1024px) {
    .text-display { font-size: 48px; }
    .text-h1 { font-size: 40px; }
    .text-h2 { font-size: 32px; }
    .text-h3 { font-size: 28px; }
    .text-body-large { font-size: 20px; }
    .text-body { font-size: 18px; }
    .text-small { font-size: 16px; }
  }

  /* 焦点样式 - 可访问性 */
  *:focus-visible {
    outline: 2px solid var(--ocean-blue);
    outline-offset: 2px;
  }

  /* 选择样式 */
  ::selection {
    background: rgba(255, 183, 3, 0.3);
    color: inherit;
  }
}

@layer components {
  /* 按钮组件 - 严格按照设计指南 */
  .btn {
    @apply inline-flex items-center justify-center font-medium rounded-md transition-all duration-150;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    gap: var(--space-sm);
  }

  /* 按钮尺寸 */
  .btn-large {
    height: 48px;
    padding: 0 24px;
    font-size: 16px;
  }

  .btn-medium {
    height: 40px;
    padding: 0 20px;
    font-size: 14px;
  }

  .btn-small {
    height: 32px;
    padding: 0 16px;
    font-size: 14px;
  }

  /* 按钮样式变体 */
  .btn-primary {
    background-color: var(--primary-yellow);
    color: #1f2937; /* 深色文字 */
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-card);
  }

  .btn-primary:hover {
    filter: brightness(1.1);
    box-shadow: var(--shadow-hover);
  }

  .btn-primary:active {
    filter: brightness(0.9);
    box-shadow: var(--shadow-inner);
  }

  .btn-primary:focus {
    @apply ring-yellow-300;
  }

  .btn-secondary {
    background-color: transparent;
    color: var(--ocean-blue);
    border: 2px solid var(--ocean-blue);
    border-radius: var(--radius-sm);
  }

  .btn-secondary:hover {
    background-color: var(--sky-blue);
    color: var(--ocean-blue);
  }

  .btn-secondary:focus {
    @apply ring-blue-300;
  }

  .btn-ghost {
    background-color: transparent;
    color: var(--ocean-blue);
    border: none;
    border-radius: var(--radius-sm);
  }

  .btn-ghost:hover {
    background-color: rgba(139, 202, 230, 0.1);
  }

  .btn-danger {
    background-color: var(--error-red);
    color: white;
    border-radius: var(--radius-sm);
  }

  .btn-danger:hover {
    filter: brightness(1.1);
  }

  /* 卡片组件 */
  .card {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    transition: box-shadow 0.2s ease;
  }

  .card:hover {
    box-shadow: var(--shadow-hover);
  }

  .card-padding {
    padding: var(--space-xl); /* 24px */
  }

  .card-padding-mobile {
    padding: var(--space-lg); /* 16px */
  }

  @media (min-width: 768px) {
    .card-padding-mobile {
      padding: var(--space-xl); /* 24px on desktop */
    }
  }

  /* 输入框组件 */
  .input {
    height: 48px; /* 触摸友好 */
    border: 2px solid #d1d5db; /* 默认灰色 */
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-size: 16px; /* 防止移动端缩放 */
    background-color: white;
    transition: all 0.15s ease;
    width: 100%;
  }

  .input:focus {
    border-color: var(--ocean-blue);
    box-shadow: 0 0 0 3px rgba(33, 158, 188, 0.1);
  }

  .input-error {
    border-color: var(--error-red);
  }

  .input-error:focus {
    border-color: var(--error-red);
    box-shadow: 0 0 0 3px rgba(247, 37, 133, 0.1);
  }

  .input-success {
    border-color: var(--success-green);
  }

  .input-success:focus {
    border-color: var(--success-green);
    box-shadow: 0 0 0 3px rgba(6, 214, 160, 0.1);
  }

  .input:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
  }

  /* 标签组件 */
  .label {
    @apply block font-medium text-gray-700 mb-2;
    font-size: 14px;
  }

  /* 导航组件 */
  .navbar {
    height: 64px; /* 设计指南规定 */
    background-color: white;
    box-shadow: var(--shadow-card);
    border-bottom: 1px solid #e5e7eb;
  }

  .nav-item {
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    color: #6b7280;
    font-weight: 500;
    transition: all 0.15s ease;
    font-size: 14px;
  }

  .nav-item:hover {
    color: var(--ocean-blue);
    background-color: rgba(139, 202, 230, 0.1);
  }

  .nav-item-active {
    color: var(--ocean-blue);
    background-color: var(--soft-pink);
  }

  /* Logo */
  .logo {
    height: 28px; /* 设计指南规定 */
    font-weight: 700;
    color: var(--ocean-blue);
  }

  /* 用户头像 */
  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--ocean-blue);
    color: white;
    @apply flex items-center justify-center font-semibold text-sm;
  }

  /* 反馈组件 */
  .alert {
    padding: var(--space-lg);
    border-radius: var(--radius-md);
    border-width: 1px;
    @apply flex items-start gap-3;
  }

  .alert-success {
    background-color: rgba(6, 214, 160, 0.1);
    border-color: var(--success-green);
    color: #065f46;
  }

  .alert-error {
    background-color: rgba(247, 37, 133, 0.1);
    border-color: var(--error-red);
    color: #991b1b;
  }

  .alert-warning {
    background-color: rgba(247, 127, 0, 0.1);
    border-color: var(--warning-amber);
    color: #92400e;
  }

  .alert-info {
    background-color: rgba(0, 119, 182, 0.1);
    border-color: var(--info-blue);
    color: #1e40af;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-200;
    border-top-color: var(--ocean-blue);
  }

  /* 游戏化元素 */
  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: var(--radius-xs);
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-yellow), var(--energy-orange));
    transition: width 0.3s ease;
  }

  .badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
    background-color: var(--primary-yellow);
    color: #1f2937;
  }

  /* 容器系统 */
  .container-narrow {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
  }

  .container-wide {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
  }

  .container-full {
    padding: 0 var(--space-lg);
  }

  @media (min-width: 768px) {
    .container-narrow,
    .container-wide,
    .container-full {
      padding: 0 var(--space-xl);
    }
  }

  /* 区域间距 */
  .section {
    padding: var(--space-3xl) 0; /* 48px 垂直间距 */
  }

  .section-sm {
    padding: var(--space-xl) 0; /* 24px 垂直间距 */
  }

  .section-lg {
    padding: var(--space-4xl) 0; /* 64px 垂直间距 */
  }

  /* 网格布局 */
  .grid-cards {
    display: grid;
    gap: var(--space-xl);
    grid-template-columns: 1fr;
  }

  @media (min-width: 640px) {
    .grid-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .grid-cards {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  /* 英雄区域 */
  .hero {
    background: linear-gradient(135deg, var(--sky-blue), var(--soft-pink));
    padding: var(--space-4xl) 0;
  }

  .hero-title {
    @apply text-display font-bold text-center mb-4;
    color: #1f2937;
  }

  .hero-subtitle {
    @apply text-body-large text-center mb-8;
    color: #4b5563;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  /* 特殊效果 */
  .highlight {
    background: linear-gradient(120deg, var(--primary-yellow) 0%, var(--primary-yellow) 100%);
    background-repeat: no-repeat;
    background-size: 100% 0.2em;
    background-position: 0 88%;
  }

  .glass {
    backdrop-filter: blur(12px);
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

@layer utilities {
  /* 间距工具类 */
  .gap-xs { gap: var(--space-xs); }
  .gap-sm { gap: var(--space-sm); }
  .gap-md { gap: var(--space-md); }
  .gap-lg { gap: var(--space-lg); }
  .gap-xl { gap: var(--space-xl); }
  .gap-2xl { gap: var(--space-2xl); }
  .gap-3xl { gap: var(--space-3xl); }
  .gap-4xl { gap: var(--space-4xl); }

  /* 内边距工具类 */
  .p-xs { padding: var(--space-xs); }
  .p-sm { padding: var(--space-sm); }
  .p-md { padding: var(--space-md); }
  .p-lg { padding: var(--space-lg); }
  .p-xl { padding: var(--space-xl); }
  .p-2xl { padding: var(--space-2xl); }
  .p-3xl { padding: var(--space-3xl); }
  .p-4xl { padding: var(--space-4xl); }

  /* 外边距工具类 */
  .m-xs { margin: var(--space-xs); }
  .m-sm { margin: var(--space-sm); }
  .m-md { margin: var(--space-md); }
  .m-lg { margin: var(--space-lg); }
  .m-xl { margin: var(--space-xl); }
  .m-2xl { margin: var(--space-2xl); }
  .m-3xl { margin: var(--space-3xl); }
  .m-4xl { margin: var(--space-4xl); }

  /* 边距顶部 */
  .mt-xs { margin-top: var(--space-xs); }
  .mt-sm { margin-top: var(--space-sm); }
  .mt-md { margin-top: var(--space-md); }
  .mt-lg { margin-top: var(--space-lg); }
  .mt-xl { margin-top: var(--space-xl); }
  .mt-2xl { margin-top: var(--space-2xl); }
  .mt-3xl { margin-top: var(--space-3xl); }
  .mt-4xl { margin-top: var(--space-4xl); }

  /* 边距底部 */
  .mb-xs { margin-bottom: var(--space-xs); }
  .mb-sm { margin-bottom: var(--space-sm); }
  .mb-md { margin-bottom: var(--space-md); }
  .mb-lg { margin-bottom: var(--space-lg); }
  .mb-xl { margin-bottom: var(--space-xl); }
  .mb-2xl { margin-bottom: var(--space-2xl); }
  .mb-3xl { margin-bottom: var(--space-3xl); }
  .mb-4xl { margin-bottom: var(--space-4xl); }

  /* 动画 */
  .animate-fade-in {
    animation: fadeIn 0.4s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 0.6s ease-in-out;
  }
  
  .animate-pulse-gentle {
    animation: pulseGentle 2s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float 3s ease-in-out 0.5s infinite;
  }
  
  .animate-float-slow {
    animation: float 5s ease-in-out infinite;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes bounceGentle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
  }
  
  @keyframes pulseGentle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.9; transform: scale(1.05); }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }

  /* 触摸友好 */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }

  /* 可访问性 */
  .sr-only-focusable:not(:focus) {
    @apply sr-only;
  }
}