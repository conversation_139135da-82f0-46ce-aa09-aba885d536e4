@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Import Google Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap');

  /* Root variables for AI Fun Lab */
  :root {
    --ai-primary: #FFB703;
    --ai-secondary: #219EBC;
    --ai-accent: #FB8500;
    --ai-background: #FFFFFF;
    --ai-surface: #FFDDD2;
  }

  /* Base styles */
  html {
    font-family: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-white text-gray-900 min-h-screen;
    font-size: 16px; /* Minimum font size for accessibility */
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-ai-blue;
  }

  /* Button focus for keyboard navigation */
  button:focus-visible,
  a:focus-visible {
    @apply ring-2 ring-ai-blue ring-offset-2;
  }
}

@layer components {
  /* Child-friendly button styles */
  .btn-primary {
    @apply bg-ai-yellow text-gray-900 px-6 py-3 rounded-lg font-medium shadow-button;
    @apply hover:bg-yellow-400 hover:shadow-card-hover active:scale-95;
    @apply transition-all duration-150 ease-out;
    @apply focus:ring-2 focus:ring-ai-yellow focus:ring-offset-2;
    min-height: 48px; /* Touch-friendly */
  }

  .btn-secondary {
    @apply bg-white border-2 border-ai-blue text-ai-blue px-6 py-3 rounded-lg font-medium;
    @apply hover:bg-ai-sky hover:border-ai-blue active:scale-95;
    @apply transition-all duration-150 ease-out;
    @apply focus:ring-2 focus:ring-ai-blue focus:ring-offset-2;
    min-height: 48px;
  }

  .btn-ghost {
    @apply bg-transparent text-ai-blue px-6 py-3 rounded-lg font-medium;
    @apply hover:bg-ai-pink active:scale-95;
    @apply transition-all duration-150 ease-out;
    min-height: 48px;
  }

  /* Card component */
  .card {
    @apply bg-white rounded-lg shadow-card p-6;
    @apply hover:shadow-card-hover transition-shadow duration-200;
  }

  /* Input field styles */
  .input-field {
    @apply w-full px-4 py-3 border-2 border-gray-200 rounded-lg;
    @apply focus:border-ai-blue focus:ring-2 focus:ring-ai-blue focus:ring-opacity-20;
    @apply transition-colors duration-150;
    min-height: 48px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .input-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500;
  }

  .input-success {
    @apply border-green-500 focus:border-green-500 focus:ring-green-500;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-gray-700 hover:text-ai-blue px-3 py-2 rounded-md font-medium;
    @apply transition-colors duration-150;
  }

  .nav-link-active {
    @apply text-ai-blue bg-ai-pink;
  }

  /* Loading animations */
  .loading-spinner {
    @apply animate-spin rounded-full border-4 border-gray-200 border-t-ai-blue;
  }

  /* Child-friendly alert styles */
  .alert-success {
    @apply bg-green-50 border border-green-200 text-green-800 p-4 rounded-lg;
  }

  .alert-error {
    @apply bg-red-50 border border-red-200 text-red-800 p-4 rounded-lg;
  }

  .alert-warning {
    @apply bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded-lg;
  }

  .alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-800 p-4 rounded-lg;
  }
}

@layer utilities {
  /* Accessibility utilities */
  .sr-only-focusable:not(:focus) {
    @apply sr-only;
  }

  /* Touch-friendly utilities */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }

  /* Text size utilities for children */
  .text-child-xs { font-size: 14px; }
  .text-child-sm { font-size: 16px; }
  .text-child-base { font-size: 18px; }
  .text-child-lg { font-size: 20px; }
  .text-child-xl { font-size: 24px; }

  /* Gradient backgrounds */
  .bg-gradient-rainbow {
    background: linear-gradient(135deg, #8ECAE6 0%, #FFB703 50%, #FB8500 100%);
  }

  .bg-gradient-sky {
    background: linear-gradient(135deg, #8ECAE6 0%, #FFDDD2 100%);
  }
}
