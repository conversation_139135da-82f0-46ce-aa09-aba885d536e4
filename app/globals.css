@import "tailwindcss";

@layer base {
  /* Import modern fonts */
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&display=swap');

  /* AI Fun Lab Design System Variables */
  :root {
    /* Primary Brand Colors */
    --ai-primary: #6366f1;
    --ai-primary-light: #818cf8;
    --ai-primary-dark: #4f46e5;
    
    /* Secondary Colors */
    --ai-secondary: #10b981;
    --ai-accent: #f59e0b;
    --ai-warning: #ef4444;
    
    /* Child-friendly Colors */
    --ai-yellow: #fbbf24;
    --ai-blue: #3b82f6;
    --ai-green: #10b981;
    --ai-purple: #8b5cf6;
    --ai-pink: #ec4899;
    --ai-orange: #f97316;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-fun: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --gradient-sky: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --gradient-sunset: linear-gradient(135deg, #ff9a8b 0%, #ffeaa7 100%);
    
    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-500: #6b7280;
    --gray-700: #374151;
    --gray-900: #111827;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Spacing Scale */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
  }

  /* Base Styles */
  html {
    font-family: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
    line-height: 1.6;
  }

  body {
    @apply bg-gray-50 text-gray-900 min-h-screen;
    font-size: 16px;
    letter-spacing: -0.01em;
  }

  /* Headings with child-friendly font */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Fredoka', 'Inter', 'Noto Sans SC', system-ui, sans-serif;
    line-height: 1.2;
    font-weight: 600;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-blue-500;
  }

  /* Selection styles */
  ::selection {
    background: rgba(99, 102, 241, 0.2);
    color: inherit;
  }
}

@layer components {
  /* Modern Button System */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg;
    @apply transition-all duration-200 ease-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    min-height: 44px;
    gap: 0.5rem;
  }

  .btn-primary {
    @apply bg-indigo-600 text-white shadow-md;
    @apply hover:bg-indigo-700 hover:shadow-lg hover:-translate-y-0.5;
    @apply active:translate-y-0 active:shadow-md;
    @apply focus:ring-indigo-500;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 shadow-sm;
    @apply hover:bg-gray-50 hover:border-gray-400 hover:shadow-md;
    @apply focus:ring-indigo-500;
  }

  .btn-fun {
    background: var(--gradient-fun);
    @apply text-white shadow-lg font-semibold;
    @apply hover:shadow-xl hover:-translate-y-1;
    @apply active:translate-y-0 active:shadow-lg;
    @apply focus:ring-orange-400;
  }

  .btn-ghost {
    @apply bg-transparent text-indigo-600 border border-transparent;
    @apply hover:bg-indigo-50 hover:text-indigo-700;
    @apply focus:ring-indigo-500;
  }

  .btn-large {
    @apply px-8 py-4 text-base;
    min-height: 52px;
  }

  .btn-small {
    @apply px-4 py-2 text-xs;
    min-height: 36px;
  }

  /* Modern Card System */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100;
    @apply transition-all duration-300 ease-out;
  }

  .card-hover {
    @apply hover:shadow-xl hover:-translate-y-1 hover:border-gray-200;
  }

  .card-interactive {
    @apply cursor-pointer;
    @apply hover:shadow-2xl hover:-translate-y-2 hover:border-indigo-200;
    @apply active:translate-y-0 active:shadow-lg;
  }

  .card-gradient {
    background: var(--gradient-primary);
    @apply text-white border-0 shadow-xl;
  }

  .card-fun {
    background: var(--gradient-fun);
    @apply text-gray-800 border-0 shadow-xl;
  }

  /* Modern Form Controls */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }

  .form-input {
    @apply w-full px-4 py-3 text-base border border-gray-300 rounded-lg;
    @apply bg-white placeholder-gray-400;
    @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent;
    @apply transition-all duration-200;
    min-height: 44px;
  }

  .form-input:focus {
    @apply shadow-lg;
  }

  .form-input-error {
    @apply border-red-500 focus:ring-red-500;
  }

  .form-input-success {
    @apply border-green-500 focus:ring-green-500;
  }

  .form-textarea {
    @apply form-input resize-none;
    min-height: 120px;
  }

  /* Alert System */
  .alert {
    @apply p-4 rounded-lg border shadow-sm;
  }

  .alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
  }

  .alert-error {
    @apply bg-red-50 border-red-200 text-red-800;
  }

  .alert-warning {
    @apply bg-amber-50 border-amber-200 text-amber-800;
  }

  .alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }

  /* Navigation */
  .nav-link {
    @apply px-3 py-2 text-sm font-medium text-gray-600 rounded-md;
    @apply transition-all duration-200;
    @apply hover:text-gray-900 hover:bg-gray-100;
  }

  .nav-link-active {
    @apply text-indigo-600 bg-indigo-50;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-indigo-600;
  }

  .skeleton {
    @apply bg-gray-200 rounded animate-pulse;
  }

  /* Hero Section */
  .hero {
    background: var(--gradient-primary);
    @apply relative overflow-hidden;
  }

  .hero-content {
    @apply relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    @apply py-24 text-center text-white;
  }

  .hero-title {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold mb-6;
    font-family: 'Fredoka', 'Inter', 'Noto Sans SC', system-ui, sans-serif;
  }

  .hero-subtitle {
    @apply text-xl sm:text-2xl mb-8 text-white/90;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  /* Feature Cards */
  .feature-card {
    @apply card card-hover p-8 text-center;
  }

  .feature-icon {
    @apply w-16 h-16 mx-auto mb-4 p-4 rounded-full;
    background: var(--gradient-fun);
  }

  .feature-title {
    @apply text-xl font-semibold mb-3 text-gray-900;
    font-family: 'Fredoka', 'Inter', 'Noto Sans SC', system-ui, sans-serif;
  }

  .feature-description {
    @apply text-gray-600 leading-relaxed;
  }

  /* Stats/Metrics */
  .metric-card {
    @apply card p-6;
  }

  .metric-icon {
    @apply w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4;
  }

  .metric-value {
    @apply text-2xl font-bold text-gray-900;
  }

  .metric-label {
    @apply text-sm text-gray-500 mt-1;
  }

  /* Container System */
  .container-full {
    @apply w-full;
  }

  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-wide {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Section Spacing */
  .section {
    @apply py-16 lg:py-24;
  }

  .section-sm {
    @apply py-8 lg:py-12;
  }

  .section-lg {
    @apply py-24 lg:py-32;
  }
}

@layer utilities {
  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Gradient Text */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-fun {
    background: var(--gradient-fun);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Child-friendly text sizes */
  .text-child-xs { font-size: 14px; line-height: 1.4; }
  .text-child-sm { font-size: 16px; line-height: 1.5; }
  .text-child-base { font-size: 18px; line-height: 1.6; }
  .text-child-lg { font-size: 20px; line-height: 1.6; }
  .text-child-xl { font-size: 24px; line-height: 1.4; }
  .text-child-2xl { font-size: 28px; line-height: 1.3; }
  .text-child-3xl { font-size: 32px; line-height: 1.2; }

  /* Background Utilities */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-fun {
    background: var(--gradient-fun);
  }

  .bg-gradient-sky {
    background: var(--gradient-sky);
  }

  .bg-gradient-sunset {
    background: var(--gradient-sunset);
  }

  /* Animation Utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes scaleIn {
    0% { transform: scale(0.9); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes slideUp {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }

  /* Responsive Grid */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  /* Accessibility */
  .sr-only-focusable:not(:focus) {
    @apply sr-only;
  }

  /* Touch-friendly */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }

  /* Scrolling */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-hidden {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }
}