{"permissions": {"allow": ["Bash(pnpm create:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(export PNPM_HOME=\"/Users/<USER>/Library/pnpm\")", "Bash(export PATH=\"$PNPM_HOME:$PATH\")", "Bash(pnpm add:*)", "Bash(pnpm dlx prisma migrate:*)", "Bash(pnpm lint:*)", "Bash(pnpm type-check:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(pnpm run:*)", "Bash(npm run build:*)", "Bash(npm run lint)"], "deny": []}}